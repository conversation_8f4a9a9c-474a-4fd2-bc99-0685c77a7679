<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zygo AI IDE v6.2</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.47.0/min/vs/loader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js" onload="console.log('JSZip loaded from primary CDN')" onerror="loadJSZipBackup()"></script>
    <script>
        function loadJSZipBackup() {
            console.warn('Primary JSZip CDN failed, loading backup...');
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/jszip@3.10.1/dist/jszip.min.js';
            script.onload = () => console.log('JSZip loaded from backup CDN');
            script.onerror = () => console.error('All JSZip CDNs failed');
            document.head.appendChild(script);
        }
    </script>

    <!-- 【紧急修复】在head中定义关键函数，确保它们在页面加载时立即可用 -->
    <script>
        function toggleForms() {
            console.log('toggleForms called from head');
            try {
                const loginForm = document.getElementById('login-form-container');
                const registerForm = document.getElementById('register-form-container');
                if (loginForm && registerForm) {
                    const isLoginVisible = loginForm.style.display !== 'none';
                    loginForm.style.display = isLoginVisible ? 'none' : 'flex';
                    registerForm.style.display = isLoginVisible ? 'flex' : 'none';
                    console.log('toggleForms executed successfully');
                } else {
                    console.warn('Form elements not found');
                }
            } catch (error) {
                console.error('toggleForms error:', error);
            }
        }

        function togglePasswordVisibility(inputId, button) {
            console.log('togglePasswordVisibility called from head with:', inputId);
            try {
                const input = document.getElementById(inputId);
                if (input && button) {
                    const eyeOpen = button.querySelector('.eye-open');
                    const eyeClosed = button.querySelector('.eye-closed');
                    if (eyeOpen && eyeClosed) {
                        if (input.type === 'password') {
                            input.type = 'text';
                            eyeOpen.style.display = 'none';
                            eyeClosed.style.display = 'block';
                        } else {
                            input.type = 'password';
                            eyeOpen.style.display = 'block';
                            eyeClosed.style.display = 'none';
                        }
                        console.log('togglePasswordVisibility executed successfully');
                    } else {
                        console.warn('Eye icons not found');
                    }
                } else {
                    console.warn('Input or button not found');
                }
            } catch (error) {
                console.error('togglePasswordVisibility error:', error);
            }
        }

        console.log('Head functions loaded - toggleForms:', typeof toggleForms, 'togglePasswordVisibility:', typeof togglePasswordVisibility);
    </script>

    <style>
        /* [保留所有您之前的 :root, *, html/body, button, input 等基础样式] */
        :root {
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            --bg-base: #12121c;
            --bg-surface: #1a1a28;
            --bg-surface-hover: #242435;
            --bg-glass: rgba(26, 26, 40, 0.6);
            --border-color: rgba(128, 128, 160, 0.2);
            --text-primary: #e0e0ff;
            --text-secondary: #9090b0;
            --accent-primary: #6a5acd;
            --accent-primary-glow: rgba(106, 90, 205, 0.5);
            --accent-success: #28a745;
            --accent-error: #dc3545;
            --accent-warn: #ffc107;
            --scrollbar-thumb: #3a3a5a;
            --scrollbar-thumb-hover: #4a4a6a;
        }

        .status-actions-container {
            background-color: var(--bg-surface);
            border: 1px solid var(--accent-primary);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            box-shadow: 0 0 20px -5px var(--accent-primary-glow);
        }
        .status-actions-container p {
            font-weight: 500;
            font-size: 1.1em;
        }
        .status-actions-container .actions {
            display: flex;
            gap: 15px;
        }

        html[data-theme='light'] {
            --bg-base: #f4f5fa;
            --bg-surface: #ffffff;
            --bg-surface-hover: #eef0f6;
            --bg-glass: rgba(255, 255, 255, 0.6);
            --border-color: rgba(0, 0, 0, 0.1);
            --text-primary: #12121c;
            --text-secondary: #5a5a72;
            --scrollbar-thumb: #c1c1c1;
            --scrollbar-thumb-hover: #a8a8a8;
        }
        * { box-sizing: border-box; margin: 0; padding: 0; }
        html, body {
            height: 100vh; width: 100vw; overflow: auto;
            background-color: var(--bg-base); color: var(--text-primary);
            font-family: var(--font-family); font-size: 14px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        input[type=number] {
            -moz-appearance: textfield;
        }
        ::-webkit-scrollbar { width: 8px; height: 8px; }
        ::-webkit-scrollbar-track { background: transparent; }
        ::-webkit-scrollbar-thumb {
            background-color: var(--scrollbar-thumb);
            border-radius: 4px; border: 2px solid transparent;
            background-clip: content-box;
        }
        ::-webkit-scrollbar-thumb:hover { background-color: var(--scrollbar-thumb-hover); }
        button, input, select, textarea {
            font-family: inherit; color: inherit; background-color: var(--bg-surface);
            border: 1px solid var(--border-color); border-radius: 6px;
            padding: 8px 12px; font-size: 1em; transition: all 0.2s ease;
        }
        button {
            cursor: pointer; background-color: var(--accent-primary);
            border-color: var(--accent-primary); color: white; font-weight: 500;
            box-shadow: 0 0 15px 0 rgba(0,0,0,0.2);
        }
        button:hover:not(:disabled) {
            background-color: #7b68ee;
            box-shadow: 0 0 20px -5px var(--accent-primary-glow);
        }
        button:disabled { background-color: var(--text-secondary); cursor: not-allowed; opacity: 0.7; }
        input:focus, textarea:focus, select:focus {
            outline: none; border-color: var(--accent-primary);
            box-shadow: 0 0 15px -5px var(--accent-primary-glow);
        }
        button.secondary { background-color: var(--bg-surface-hover); border-color: var(--border-color); color: var(--text-primary); }
        .ide-container { display: flex; height: 100vh; }
        #activity-bar {
            width: 55px; background-color: var(--bg-base);
            border-right: 1px solid var(--border-color); padding: 10px 0;
            display: flex; flex-direction: column; align-items: center;
            justify-content: space-between; flex-shrink: 0;
        }
        .activity-group { display: flex; flex-direction: column; align-items: center; gap: 15px; }
        .activity-btn {
            width: 40px; height: 40px; display: flex; justify-content: center; align-items: center;
            border-radius: 8px; cursor: pointer; transition: background-color 0.2s ease;
            border: none; background: none; padding: 0;
        }
        .activity-btn svg { width: 24px; height: 24px; stroke: var(--text-secondary); transition: all 0.2s ease; }
        .activity-btn:hover { background-color: var(--bg-surface-hover); }
        .activity-btn.active { background-color: var(--accent-primary); }
        .activity-btn.active svg { stroke: white; }
        .content-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            overflow: visible;
        }
        /* 修改主视图以适应问题面板 */
        #main-view {
            flex-grow: 1;
            position: relative;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: visible; /* 允许内容滚动 */
        }

        /* [新增] 优化问题面板布局的样式 (增强版 v2) */
        #problems-panel-content {
            padding: 5px 10px; /* 减小面板的整体内边距 */
        }
        #problems-panel-content h4 {
            margin: 2px 0 4px; /* 进一步减小标题的垂直外边距 */
            font-size: 0.9em;
            padding-bottom: 3px;
            font-family: var(--font-family);
            font-weight: 500;
            border-bottom: 1px solid var(--border-color);
        }
        #problems-panel-content ul {
            list-style: none;
            padding: 0;
            margin: 0; /* 移除列表的上边距 */
        }
        #problems-panel-content li {
            padding: 2px 4px; /* 维持列表项的最小垂直内边距 */
            border-radius: 4px;
            cursor: pointer;
            display: flex; /* 核心修改：使用 Flex 布局 */
            align-items: baseline; /* 基线对齐，文本看起来更整齐 */
            gap: 8px; /* 在项目之间创建间隙 */
            white-space: nowrap; /* 防止子元素内部换行 */
        }
        #problems-panel-content li > span:nth-child(3) {
            white-space: normal; /* 允许错误消息本身换行 */
            word-break: break-all;
        }

        .view {
            display: none;
            flex-grow: 1;
            flex-direction: column; /* 确保视图内部也是flex列布局 */
        }
        .view.active {
            display: flex;
        }
        #status-bar {
            height: 32px; background-color: var(--bg-surface);
            border-top: 1px solid var(--border-color); display: flex;
            align-items: center; padding: 0 15px; gap: 20px;
            font-size: 0.9em; color: var(--text-secondary); flex-shrink: 0;
        }
        .status-item { display: flex; align-items: center; gap: 6px; }
        .status-item svg { width: 14px; height: 14px; stroke: var(--text-secondary); }
        #status-user-actions { margin-left: auto; display: flex; align-items: center; gap: 15px; }
        /* [最终修正] 默认状态：保持内容水平垂直居中 */
        #view-ai-workflow {
            justify-content: center;
            align-items: center;
            transition: background-color 0.4s ease, backdrop-filter 0.4s ease;
        }
        /* [最终修正] 新增状态：当工作流激活时，切换为从上到下、拉伸布局的日志模式 */
        #view-ai-workflow.workflow-active {
            flex-direction: column;
            justify-content: flex-start;
            align-items: stretch;
            overflow-y: auto; /* 允许工作流视图滚动 */
        }
        #status-problems .status-item {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        #status-problems .status-item:hover {
            opacity: 1;
        }

        #status-problems .status-item.active {
            opacity: 1;
            text-decoration: underline;
        }
        #initial-prompt-container {
            width: 100%;
            height: 100%;
            max-width: 900px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            gap: 20px;
            position: relative;
            /* 新增下面这一行 */
            transition: height 0.4s ease;
        }
        #initial-prompt-container h1 { font-size: 2em; font-weight: 500; color: var(--text-primary); }
        #initial-prompt-container p { color: var(--text-secondary); text-align: center; }
        #raw-text-input { width: 100%; padding: 15px; font-size: 1.1em; background-color: var(--bg-surface); }
        #initial-submit-button { padding: 12px 25px; font-size: 1.1em; }
        #workflow-stream-container {
            display: none;
            width: 100%;
            padding: 20px;
            font-family: var(--font-mono);
            font-size: 15px;
            line-height: 1.7;
            /* --- 核心修改：让容器在flex布局中正确滚动 --- */
            flex-grow: 1; /* 替换 height: 100% */
            min-height: 0;  /* 允许在flex容器中正确收缩 */
            overflow-y: auto; /* 这行保持不变，但现在会生效 */
        }
        .stream-entry { margin-bottom: 8px; }
        .stream-entry .timestamp { color: var(--text-secondary); margin-right: 15px; }
        .stream-entry .status-running { color: var(--accent-primary); font-weight: bold; }
        .stream-entry .status-completed { color: var(--accent-success); font-weight: bold; }
        .stream-entry .status-failed { color: var(--accent-error); font-weight: bold; }

        /* 智能日志系统的新样式 */
        .stream-entry .status-thinking { color: #9b59b6; font-weight: bold; }  /* 紫色 - 思考 */
        .stream-entry .status-coding { color: #3498db; font-weight: bold; }    /* 蓝色 - 编码 */
        .stream-entry .status-compiling { color: #f39c12; font-weight: bold; } /* 橙色 - 编译 */
        .stream-entry .status-analyzing { color: #e67e22; font-weight: bold; } /* 深橙色 - 分析 */
        .stream-entry .status-fixing { color: #e74c3c; font-weight: bold; }    /* 红色 - 修复 */

        /* 旋转思考圆圈 */
        .thinking-spinner {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 8px;
            vertical-align: middle;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 打字机效果 */
        .typewriter-text {
            overflow: hidden;
            border-right: 2px solid transparent;
            white-space: nowrap;
            animation: blink-caret 1s step-end infinite;
        }

        .typewriter-text.typing {
            border-right-color: currentColor;
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: currentColor; }
        }
        .stream-entry .log-line { white-space: pre-wrap; word-break: break-all; }
        .output-block {
            border: 1px solid var(--border-color); border-radius: 8px;
            margin-top: 10px; margin-bottom: 15px;
            background-color: var(--bg-surface);
            overflow: hidden;
        }
        .output-block-header {
            font-weight: 500; color: var(--text-secondary);
            padding: 8px 15px;
            border-bottom: 1px solid var(--border-color);
            background-color: rgba(0,0,0,0.1);
        }
        .output-block-content {
            padding: 15px;
            white-space: pre-wrap;
            word-break: break-all;
            color: var(--text-primary);
            max-height: 400px;
            overflow-y: auto;
            overflow-wrap: break-word;
        }
        .output-block-content pre, .output-block-content code {
            margin: 0; padding: 0 !important; background: transparent !important;
        }
        #view-file-explorer { flex-direction: row; }

        #editor-area-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* 允许收缩 */
            overflow: hidden;
        }
        /* 【新增】问题面板样式 */
        #problems-panel {
            flex-shrink: 0;
            border-top: 1px solid var(--border-color);
            background-color: var(--bg-surface);
            display: none;
            flex-direction: column;
            width: 100%;
            overflow: hidden;
            min-height: 100px; /* 添加最小高度 */
            height: 200px; /* 设置默认高度 */
        }

        /* 确保调节器可见且易于拖拽 */
        #panel-resizer {
            height: 1px;
            background-color: var(--border-color);
            cursor: ns-resize;
            flex-shrink: 0;
            display: none;
            transition: background-color 0.2s ease;
        }

        #panel-resizer:hover {
            background-color: var(--accent-primary);
        }

        #problems-panel-header {
            padding: 5px 15px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }
        /* 修改内容区域样式 */
        #problems-panel-content {
            overflow: auto;
            flex-grow: 1;
            padding: 10px;
            font-family: var(--font-mono);
            font-size: 0.9em;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            min-height: 0; /* 允许内容区域收缩 */
        }

        #status-problems .icon-error { stroke: var(--accent-error); }
        #status-problems .icon-warning { stroke: var(--accent-warn); }
        #status-problems .icon-info { stroke: var(--accent-primary); }
        #file-tree-panel {
            width: 280px; padding: 15px; border-right: 1px solid var(--border-color);
            overflow-y: auto; flex-shrink: 0; display: flex; flex-direction: column;
        }
        #file-tree-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        #file-tree-header h3 { margin: 0; }
        #file-tree-container { flex-grow: 1; overflow-y: auto; }
        #code-editor-panel {
            flex-grow: 1; /* 这是关键，让编辑器占据所有可用空间 */
            display: flex;
            flex-direction: column;
        }
        #code-editor-header { padding: 10px 15px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; }
        #code-editor-textarea {
            flex-grow: 1; background-color: var(--bg-base); border: none; padding: 15px;
            resize: none; font-family: var(--font-mono); font-size: 15px; line-height: 1.7;
        }
        #file-tree-container ul { list-style-type: none; padding-left: 15px; }
        .tree-item { cursor: pointer; display: flex; align-items: center; padding: 4px 5px; border-radius: 4px; }
        .tree-item:hover { background-color: var(--bg-surface-hover); }
        .tree-item.active { background-color: var(--accent-primary); color: white; }
        .tree-item .icon { margin-right: 8px; width: 16px; text-align: center; }
        .folder .icon::before { content: '▶'; display: inline-block; transition: transform 0.1s; }
        .folder.open > .tree-item .icon::before { transform: rotate(90deg); }
        .file .icon::before { content: '📄'; }
        .nested { display: none; }
        .folder.open > .nested { display: block; }
        #view-device-manager { padding: 20px; overflow-y: auto; }
        .device-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(420px, 1fr)); gap: 20px; }
        .device-card {
            background-color: var(--bg-surface); border: 1px solid var(--border-color); border-radius: 8px;
            padding: 20px; display: flex; flex-direction: column; gap: 15px; transition: all 0.2s ease;
        }
        .device-card:hover { border-color: var(--accent-primary); box-shadow: 0 0 15px -5px var(--accent-primary-glow); }
        .device-card-header { display: flex; justify-content: space-between; align-items: center; }
        .device-card-header h3 { font-size: 1.1em; font-weight: 500; }
        .device-card-body p { font-size: 0.9em; color: var(--text-secondary); }
        .device-card-actions { display: flex; gap: 10px; margin-top: auto; }
        .device-card-actions button { flex-grow: 1; padding: 8px; font-size: 0.9em; }
        .device-card-actions .delete-btn { background-color: var(--accent-error); }

        /* MQTT监控相关样式 */
        .mqtt-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        .mqtt-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .mqtt-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
        }
        .mqtt-status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--text-secondary);
        }
        .mqtt-status-indicator.connected {
            background-color: var(--accent-success);
            box-shadow: 0 0 6px var(--accent-success);
        }
        .mqtt-status-indicator.disconnected {
            background-color: var(--accent-error);
            box-shadow: 0 0 6px var(--accent-error);
        }
        .mqtt-status-indicator.disabled {
            background-color: var(--text-secondary);
        }
        .mqtt-controls {
            display: flex;
            gap: 8px;
        }
        .mqtt-controls button {
            padding: 6px 12px;
            font-size: 0.8em;
            border-radius: 4px;
        }
        .mqtt-log-container {
            background-color: var(--bg-base);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            padding: 12px;
            font-family: var(--font-mono);
            font-size: 0.9em;
            margin-top: 10px;
            line-height: 1.4;
        }
        .mqtt-log-entry {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 12px;
            padding: 8px;
            background: var(--bg-secondary);
            border-radius: 6px;
            border-left: 3px solid var(--accent-primary);
        }
        .mqtt-log-entry.incoming {
            border-left-color: var(--accent-success);
        }
        .mqtt-log-entry.outgoing {
            border-left-color: var(--accent-primary);
        }
        .mqtt-log-header {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 0.85em;
        }
        .mqtt-log-time {
            color: var(--text-secondary);
            min-width: 80px;
            font-weight: 500;
        }
        .mqtt-log-direction {
            min-width: 24px;
            font-weight: bold;
            font-size: 1.1em;
        }
        .mqtt-log-direction.incoming {
            color: var(--accent-success);
        }
        .mqtt-log-direction.outgoing {
            color: var(--accent-primary);
        }
        .mqtt-log-topic {
            color: var(--accent-primary);
            font-weight: 500;
            background: var(--bg-base);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .mqtt-log-payload {
            color: var(--text-primary);
            margin-top: 4px;
            padding: 8px;
            background: var(--bg-base);
            border-radius: 4px;
            word-break: break-word;
            white-space: pre-wrap;
            font-size: 0.95em;
            line-height: 1.3;
        }
        .mqtt-log-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }
        .mqtt-log-actions button {
            padding: 4px 8px;
            font-size: 0.7em;
        }

        /* MQTT配置界面样式 */
        .mqtt-config-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        .mqtt-config-section h3 {
            margin: 0 0 15px 0;
            font-size: 1.1em;
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .mqtt-config-section h3::before {
            content: '';
            width: 4px;
            height: 16px;
            background: var(--accent-primary);
            border-radius: 2px;
        }
        .mqtt-form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .mqtt-form-row > div {
            flex: 1;
        }
        .mqtt-form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .mqtt-form-group label {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.9em;
        }
        .mqtt-form-group .label-optional {
            color: var(--text-secondary);
            font-weight: normal;
        }
        .mqtt-input-with-icon {
            position: relative;
        }
        .mqtt-input-with-icon input,
        .mqtt-input-with-icon textarea {
            padding-left: 35px;
        }
        .mqtt-input-with-icon.password-wrapper input {
            padding-right: 40px; /* 为密码切换按钮留出空间 */
        }
        .mqtt-input-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 0.9em;
        }
        .mqtt-topics-help {
            font-size: 0.8em;
            color: var(--text-secondary);
            margin-top: 5px;
            line-height: 1.4;
        }
        .mqtt-monitoring-toggle {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .mqtt-monitoring-toggle:hover {
            background: var(--bg-hover);
            border-color: var(--accent-primary);
        }
        .mqtt-monitoring-toggle input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin: 0;
        }
        .mqtt-monitoring-toggle label {
            margin: 0;
            cursor: pointer;
            font-weight: 500;
        }

        /* MQTT配置对话框特殊样式 */
        #mqtt-config-dialog .dialog-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
        }
        #mqtt-config-dialog .dialog-actions button {
            flex: 1;
            padding: 10px 15px;
            font-weight: 500;
        }
        #mqtt-config-dialog .dialog-actions button.secondary {
            flex: 0 0 auto;
            min-width: 80px;
        }
        #mqtt-test-btn {
            background: var(--accent-primary);
            color: white;
            border: none;
        }
        #mqtt-test-btn:hover {
            background: var(--accent-primary-hover);
        }

        /* MQTT主题输入框样式 */
        .mqtt-topics-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 15px;
        }
        .mqtt-topic-input-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .mqtt-topic-input-row .mqtt-input-with-icon {
            flex: 1;
        }
        .mqtt-topic-input-row .mqtt-input-with-icon input {
            width: 100%;
        }
        .mqtt-topic-remove-btn {
            background: var(--accent-error);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 10px;
            cursor: pointer;
            font-size: 0.9em;
            min-width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .mqtt-topic-remove-btn:hover {
            background: var(--accent-error-hover);
        }

        /* Smart Peripheral Dialog Styles */
        .step-container {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin: 0 auto 10px auto;
            max-width: 320px;
            width: 100%;
        }

        .step-container h3 {
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-container h3::before {
            content: "";
            font-size: 1.2em;
        }

        .step-container p {
            margin-bottom: 12px;
            line-height: 1.4;
            font-size: 0.85em;
        }

        #smart-peripheral-dialog .dialog-content {
            max-height: 80vh;
            overflow-y: auto;
            padding: 25px;
        }

        #smart-peripheral-dialog h2 {
            text-align: center;
            font-size: 1.2em;
            font-weight: 500;
            margin-bottom: 20px;
        }

        #smart-peripheral-dialog .step-container {
            margin: 0 auto 15px auto;
            text-align: left;
            width: 100%; /* 确保填满容器 */
            max-width: 100%;
            padding: 20px;
        }

        #smart-peripheral-dialog .form-group {
            text-align: left;
            margin-bottom: 15px;
        }

        #smart-peripheral-dialog .dialog-actions {
            display: flex;
            justify-content: flex-end; /* 按钮靠右对齐 */
            gap: 10px;
            margin-top: 20px;
        }

        /* 智能外设对话框按钮样式优化 */
        #smart-peripheral-dialog .dialog-actions button {
            padding: 6px 15px; /* 减小按钮内边距 */
            font-size: 0.9em;
            font-weight: 500;
        }

        /* 智能外设对话框整体紧凑化 */
        #smart-peripheral-dialog .step-container h3 {
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        #smart-peripheral-dialog .step-container p {
            font-size: 0.9em;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        #smart-peripheral-dialog .form-group label {
            font-size: 0.9em;
            margin-bottom: 6px;
            display: block;
        }

        #smart-peripheral-dialog .form-group select {
            height: 40px; /* 减小选择框高度 */
            font-size: 0.95em;
        }

        /* 处理按钮的显示/隐藏状态 */
        #smart-peripheral-dialog #smart-peripheral-prev-btn,
        #smart-peripheral-dialog #smart-peripheral-submit-btn {
            display: none;
        }

        #dynamic-config-container {
            background: var(--background-secondary);
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
        }

        #pin-config-container h4,
        #additional-config-container h4 {
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #pin-config-container h4::before {
            content: "";
        }

        #additional-config-container h4::before {
            content: "";
        }

        .form-group small {
            display: block;
            margin-top: 4px;
        }
        .mqtt-topics-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .mqtt-topics-actions button {
            padding: 6px 12px;
            font-size: 0.85em;
        }
        #mqtt-test-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
        }
        dialog {
            background: var(--bg-glass); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color); border-radius: 12px; color: var(--text-primary);
            width: 90%; box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            position: fixed; top: 50%; left: 50%;
            transform: translate(-50%, -50%); margin: 0;
        }
        dialog#confirmation-dialog, dialog#device-dialog { max-width: 800px; }
        dialog#settings-dialog, dialog#project-loader-dialog { max-width: 500px; }
        dialog#mqtt-config-dialog { max-width: 800px; }
        dialog#smart-peripheral-dialog { max-width: 420px; }
        dialog::backdrop { background: rgba(0, 0, 0, 0.4); }
        .dialog-content {
            padding: 25px; display: flex; flex-direction: column; gap: 15px;
            max-height: 80vh; overflow-y: auto;
        }
        .dialog-content h2 { font-weight: 500; margin-bottom: 10px; }
        .dialog-actions { display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px; }
        #project-list {
            list-style: none;
            max-height: 400px;
            overflow-y: auto;
            padding: 0;
            margin: 0;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-surface);
        }
        #project-list::-webkit-scrollbar { width: 8px; }
        #project-list::-webkit-scrollbar-track { background: transparent; }
        #project-list::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
            border: 2px solid var(--bg-surface);
        }
        #project-list::-webkit-scrollbar-thumb:hover { background: var(--text-secondary); }
        .pagination-controls {
            display: flex; justify-content: center; align-items: center; gap: 10px;
            margin-top: 15px; padding: 10px 0; border-top: 1px solid var(--border-color);
        }
        .pagination-controls button {
            padding: 5px 10px; border: 1px solid var(--border-color);
            background: var(--bg-surface); border-radius: 4px; cursor: pointer;
        }
        .pagination-controls button:hover { background: var(--bg-surface-hover); }
        .pagination-controls button:disabled { opacity: 0.5; cursor: not-allowed; }
        .pagination-info { color: var(--text-secondary); font-size: 0.9em; }
        #project-list li {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            margin: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-surface);
        }
        #project-list li:last-child { border-bottom: none; }
        #project-list li:hover { background-color: var(--bg-surface-hover); }
        #project-list .project-name {
            cursor: pointer;
            flex-grow: 1;
        }
        #project-list .project-name:hover {
            color: var(--accent-primary);
        }
        #project-list .delete-project-btn {
            background-color: transparent;
            border: none;
            color: var(--text-secondary);
            width: 28px;
            height: 28px;
            border-radius: 50%;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
            box-shadow: none;
            margin-left: 15px;
        }
        #project-list .delete-project-btn:hover {
            background-color: var(--bg-surface-hover);
            color: var(--accent-error);
        }
        #project-list .delete-project-btn svg {
            width: 16px;
            height: 16px;
        }

        #project-list .status-badge {
            font-size: 0.8em; padding: 3px 8px; border-radius: 10px;
            color: white;
        }
        .status-badge.completed { background-color: var(--accent-success); }
        .status-badge.failed { background-color: var(--accent-error); }
        .status-badge.running { background-color: var(--accent-primary); }
        .device-task-card {
            background-color: var(--bg-surface);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        .device-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .device-card-header h5 {
            font-size: 1.1em;
            font-weight: 500;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            flex-grow: 1;
        }
        .device-card-header h5 input {
            font-size: 1em;
            font-weight: 500;
            flex-grow: 1;
        }
        .remove-task-btn {
            background-color: transparent;
            border: none;
            color: var(--text-secondary);
            width: 32px; height: 32px;
            border-radius: 50%;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
            box-shadow: none;
        }
        .remove-task-btn:hover {
            background-color: var(--bg-surface-hover);
            color: var(--accent-error);
        }
        .remove-task-btn svg {
            width: 18px;
            height: 18px;
        }
        .device-card-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px 25px;
            align-items: start;
        }
        .peripheral-wrapper {
            grid-column: 1 / -1;
            margin-top: 10px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }
        .device-card-content > div {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .device-description {
            flex-grow: 1;
            resize: vertical;
            min-height: 80px;
        }
        .remove-peripheral-btn {
            background-color: transparent;
            border: none;
            color: var(--text-secondary);
            width: 28px;
            height: 28px;
            border-radius: 50%;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
            box-shadow: none;
        }
        .remove-peripheral-btn:hover {
            background-color: var(--accent-error);
            color: white;
        }
        .remove-peripheral-btn svg {
            width: 14px;
            height: 14px;
        }
        .data-flow-line {
            display: flex;
            align-items: center;
            margin: 25px 5px;
        }
        .flow-node {
            background-color: var(--bg-surface-hover);
            padding: 8px 15px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            font-weight: 500;
            white-space: nowrap;
        }
        .flow-path {
            flex-grow: 1;
            height: 2px;
            background-color: var(--border-color);
            position: relative;
            margin: 0 15px;
        }
        .flow-arrow {
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: var(--accent-primary);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--accent-primary-glow);
            animation: flow-animation 2.5s linear infinite;
        }
        .flow-label {
            position: absolute;
            top: -22px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.85em;
            color: var(--text-secondary);
            background-color: var(--bg-base);
            padding: 0 8px;
            white-space: nowrap;
        }
        @keyframes flow-animation {
            from { left: 0; }
            to { left: 100%; }
        }
        #login-view { width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; }
        .form-container {
            display: flex; flex-direction: column; gap: 15px; width: 340px;
        }
        .form-container h2 { text-align: center; margin-bottom: 10px; font-weight: 500; }
        .form-container input { width: 100%; height: 44px; }

        /* 表单组样式 */
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .form-group label {
            font-size: 0.9em;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        .form-group input,
        .form-group select {
            width: 100%;
            height: 44px;
        }
        .password-wrapper { position: relative; display: flex; align-items: center; }
        .password-wrapper input { padding-right: 40px; }
        .password-toggle-btn {
            position: absolute; right: 12px; top: 50%; transform: translateY(-50%);
            width: 24px; height: 24px; background: transparent;
            border: none; border-radius: 4px;
            cursor: pointer; padding: 0;
            display: flex; justify-content: center; align-items: center;
            transition: all 0.2s ease;
        }
        .password-toggle-btn:hover {
            background: rgba(106, 90, 205, 0.1);
        }
        .password-toggle-btn svg {
            stroke: var(--text-secondary); width: 16px; height: 16px;
            transition: stroke 0.2s ease;
        }
        .password-toggle-btn:hover svg { stroke: var(--accent-primary); }
        .form-toggle { color: var(--accent-primary); cursor: pointer; text-align: center; font-size: 0.9em; margin-top: 10px; }
        .form-message { text-align: center; min-height: 20px; }
        .error { color: var(--accent-error); }
        .success { color: var(--accent-success); }

            /* 【核心新增】部署操作按钮的样式 */
        .deploy-action-btn {
            padding: 12px 25px;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .deploy-action-btn svg {
            width: 20px;
            height: 20px;
        }

        /* --- 文件管理器侧边栏拉伸条 --- */
        #sidebar-resizer {
            width: 3px;
            background-color: var(--border-color);
            cursor: ew-resize; /* 东西向拉伸光标 */
            flex-shrink: 0;
            transition: background-color 0.2s ease;
        }
        #sidebar-resizer:hover {
            background-color: var(--accent-primary);
        }

        /* --- 主界面 Gemini 风格动画和布局 --- */
        #initial-prompt-container {
            width: 100%;
            height: 100%;
            max-width: 900px; /* 增加最大宽度以适应新布局 */
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end; /* 内容置于底部 */
            gap: 20px;
            position: relative;
        }
        #welcome-greeting-wrapper {
            position: absolute;
            top: 25%;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }
        #welcome-greeting {
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            border-right: .15em solid var(--accent-primary);
            /* 移除初始 opacity: 0 */
        }
        @keyframes typing {
            from { width: 0 }
            to {
                width: 100%;
                border-color: transparent; /* <-- 新增此行 */
            }
        }
        @keyframes blink-caret {
            from, to { border-color: transparent }
            50% { border-color: var(--accent-primary); }
        }

        #prompt-input-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            background-color: var(--bg-surface);
            border: 1px solid var(--border-color);
            border-radius: 28px;
            padding: 8px 8px 8px 20px;
            width: 100%;
            position: relative;
            opacity: 0;
            transform: translateY(20px);
            /* 新增下面这一行，让所有变化的属性都动起来 */
            transition: all 0.4s ease-in-out;
        }
        /* --- 动画播放阶段 --- */
        #prompt-input-wrapper:focus-within {
             border-color: var(--accent-primary);
             box-shadow: 0 0 15px -5px var(--accent-primary-glow);
        }
        #raw-text-input {
            flex-grow: 1;
            border: none;
            background: transparent;
            padding: 8px 0;
            resize: none; /* 禁用右下角拉伸抓手 */
            overflow-y: auto; /* 开始时隐藏滚动条 */
            transition: all 0.4s ease-in-out;
            line-height: 1.5;
            max-height: 200px; /* 限制自动增高的高度 */
        }
        #raw-text-input:focus {
            outline: none;
            box-shadow: none;
        }
        #send-prompt-btn {
             background-color: transparent; /* <-- 修改这里 */
             color: var(--text-secondary); /* 默认颜色也需要改回普通状态 */
             opacity: 1;
             transition: all 0.4s ease-in-out;
        }
        #send-prompt-btn:not(:disabled) {
             opacity: 1;
        }
        /* --- 基础样式和过渡定义 --- */
        /* 为所有参与动画的元素添加平滑过渡 */
        #prompt-input-wrapper,
        #raw-text-input,
        #expand-input-btn,
        #send-prompt-btn {
            transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1); /* 一个更优雅的缓动函数 */
        }

        /* 初始加载动画：只在首次加载时播放 */
        #view-ai-workflow:not(.animation-finished) #welcome-greeting {
            animation:
                typing 1.5s steps(40, end) forwards,
                blink-caret 0.75s step-end 2;
        }
        #view-ai-workflow:not(.animation-finished) #prompt-input-wrapper {
            animation: slide-up 0.5s 1.5s forwards;
        }

        /* 动画播放完毕后的静态状态 */
        #view-ai-workflow.animation-finished #welcome-greeting {
            opacity: 1;
            border-color: transparent;
        }
        #view-ai-workflow.animation-finished #prompt-input-wrapper {
            opacity: 1;
            transform: translateY(0);
        }


        /* --- 竖直拉伸模式 --- */
        /* 拉伸时的背景遮罩（轻微） */
        #view-ai-workflow.fullscreen-active {
            background: rgba(18, 18, 28, 0.3);
            backdrop-filter: blur(3px);
            -webkit-backdrop-filter: blur(3px);
            transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1);
        }

        /* 输入框wrapper的竖直拉伸动画 - 分离高度和其他属性 */
        #prompt-input-wrapper {
            /* 高度变化用更长时间，其他属性用较短时间 */
            transition:
                height 3.5s cubic-bezier(0.165, 0.84, 0.44, 1),
                border-radius 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                padding 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform-origin: center bottom; /* 从底部中心开始拉伸 */
            min-height: auto; /* 确保可以缩小到内容高度 */
        }

        /* 非全屏状态下的自适应高度 */
        #view-ai-workflow:not(.fullscreen-active) #prompt-input-wrapper {
            height: auto !important;
        }

        /* 核心：输入框wrapper的“变身”效果 */
        #view-ai-workflow.fullscreen-active #prompt-input-wrapper {
            height: 90vh !important; /* 拉伸到屏幕90%高度 */
            flex-direction: column;
            align-items: stretch;
            padding: 20px;
            border-radius: 16px; /* 稍微增加圆角 */
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        }

        /* 文本域的平滑拉伸 */
        #raw-text-input {
            /* 高度变化与输入框同步，其他属性更快 */
            transition:
                height 3.5s cubic-bezier(0.165, 0.84, 0.44, 1),
                font-size 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                padding 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                line-height 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* 非全屏状态下的文本域自适应 */
        #view-ai-workflow:not(.fullscreen-active) #raw-text-input {
            height: auto !important;
            max-height: 200px; /* 限制最大高度 */
            padding: 8px 0; /* 恢复原始内边距 */
            font-size: 1em; /* 恢复原始字体大小 */
        }

        #view-ai-workflow.fullscreen-active #raw-text-input {
            height: 100%;
            max-height: none;
            font-size: 1.1em;
            padding: 35px 60px 60px 15px; /* 上边距35px避免遮挡缩小按钮，右边距60px，下边距60px避免遮挡发送按钮 */
            line-height: 1.6;
        }

        /* 按钮的平滑位置过渡 */
        #expand-input-btn {
            transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1);
        }

        #view-ai-workflow.fullscreen-active #expand-input-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 10;
            background: var(--bg-surface-hover);
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #send-prompt-btn {
            transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1);
        }

        #view-ai-workflow.fullscreen-active #send-prompt-btn {
            position: absolute;
            bottom: 15px;
            right: 15px;
            background: var(--accent-primary);
            border-radius: 8px;
            padding: 12px 16px;
            box-shadow: 0 4px 12px rgba(106, 90, 205, 0.3);
        }

        /* 图标切换的平滑过渡 */
        .icon-expand, .icon-collapse {
            transition: all 0.3s ease;
        }

        #view-ai-workflow.fullscreen-active .icon-expand {
            opacity: 0;
            transform: rotate(180deg) scale(0.8);
            display: none;
        }

        #view-ai-workflow.fullscreen-active .icon-collapse {
            opacity: 1;
            transform: rotate(0deg) scale(1);
            display: block;
        }



        @keyframes slide-up {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        /* 思考中的指示器 - 增强版 */
        #thinking-indicator {
            display: none;
            position: absolute;
            top: 35%; /* 调整位置，避免遮挡欢迎语 */
            left: 50%;
            transform: translateX(-50%);
        }

        .thinking-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(106, 90, 205, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            animation: thinking-breathe 3s ease-in-out infinite;
        }

        .thinking-icon-container {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .thinking-content .logo-icon {
            filter: drop-shadow(0 0 15px var(--accent-primary-glow));
            animation: thinking-glow 2s ease-in-out infinite alternate;
        }

        /* 围绕图标的思考粒子 */
        .thinking-particles {
            position: absolute;
            width: 80px;
            height: 80px;
            pointer-events: none;
        }

        .thinking-particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--accent-primary);
            border-radius: 50%;
            opacity: 0.7;
        }

        .thinking-particle:nth-child(1) {
            top: 10px;
            left: 50%;
            animation: thinking-orbit-1 4s linear infinite;
        }

        .thinking-particle:nth-child(2) {
            top: 50%;
            right: 10px;
            animation: thinking-orbit-2 4s linear infinite 0.8s;
        }

        .thinking-particle:nth-child(3) {
            bottom: 10px;
            left: 50%;
            animation: thinking-orbit-3 4s linear infinite 1.6s;
        }

        .thinking-particle:nth-child(4) {
            top: 50%;
            left: 10px;
            animation: thinking-orbit-4 4s linear infinite 2.4s;
        }

        .thinking-particle:nth-child(5) {
            top: 20px;
            right: 20px;
            animation: thinking-orbit-5 4s linear infinite 3.2s;
        }

        /* 思考文字 */
        .thinking-text {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            color: var(--text-secondary);
        }

        .thinking-message {
            font-weight: 500;
        }

        .thinking-dots {
            display: flex;
            gap: 2px;
        }

        .thinking-dots .dot {
            animation: thinking-dot-bounce 1.4s ease-in-out infinite;
            font-weight: bold;
            font-size: 1.2em;
        }

        .thinking-dots .dot:nth-child(1) {
            animation-delay: 0s;
        }

        .thinking-dots .dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .thinking-dots .dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        /* 思考指示器的脉冲效果 */
        #thinking-indicator.pulse .thinking-content {
            animation: thinking-pulse 0.6s ease-out, thinking-breathe 3s ease-in-out infinite;
        }

        /* 动画关键帧 */
        @keyframes thinking-breathe {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 12px 40px rgba(106, 90, 205, 0.15);
            }
        }

        @keyframes thinking-glow {
            0% {
                filter: drop-shadow(0 0 15px var(--accent-primary-glow));
            }
            100% {
                filter: drop-shadow(0 0 25px var(--accent-primary-glow));
            }
        }

        @keyframes thinking-pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            50% {
                transform: scale(1.1);
                box-shadow: 0 16px 48px rgba(106, 90, 205, 0.3);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
        }

        @keyframes thinking-dot-bounce {
            0%, 80%, 100% {
                transform: translateY(0);
                opacity: 0.7;
            }
            40% {
                transform: translateY(-8px);
                opacity: 1;
            }
        }

        @keyframes thinking-orbit-1 {
            0% { transform: rotate(0deg) translateX(30px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(30px) rotate(-360deg); }
        }

        @keyframes thinking-orbit-2 {
            0% { transform: rotate(0deg) translateX(25px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(25px) rotate(-360deg); }
        }

        @keyframes thinking-orbit-3 {
            0% { transform: rotate(0deg) translateX(35px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(35px) rotate(-360deg); }
        }

        @keyframes thinking-orbit-4 {
            0% { transform: rotate(0deg) translateX(28px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(28px) rotate(-360deg); }
        }

        @keyframes thinking-orbit-5 {
            0% { transform: rotate(0deg) translateX(32px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(32px) rotate(-360deg); }
        }

        /* 需求发送气泡动画 - 增强版 */
        #prompt-animation-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none; /* 让鼠标事件穿透 */
            overflow: hidden; /* 防止动画元素溢出 */
        }
        .prompt-bubble {
            position: absolute;
            bottom: 120px; /* 从输入框附近开始 */
            left: 50%;
            transform: translateX(-50%) scale(0.8);
            padding: 15px 25px;
            background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-surface-hover) 100%);
            border: 1px solid var(--accent-primary);
            border-radius: 25px;
            box-shadow:
                0 8px 25px rgba(0,0,0,0.15),
                0 0 20px var(--accent-primary-glow);
            animation: enhanced-bubble-flight 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            max-width: 600px;
            white-space: pre-wrap;
            word-break: break-word;
            font-size: 0.95em;
            line-height: 1.4;
            color: var(--text-primary);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* 气泡尾巴效果 */
        .prompt-bubble::before {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 16px;
            height: 16px;
            background: inherit;
            border: inherit;
            border-top: none;
            border-left: none;
            border-radius: 0 0 25px 0;
            transform: translateX(-50%) rotate(45deg);
        }

        /* 增强的飞行动画 - 流畅版本，飞向思考指示器 */
        @keyframes enhanced-bubble-flight {
            0% {
                bottom: 120px;
                left: 50%;
                transform: translateX(-50%) scale(0.8) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: translateX(-50%) scale(1) rotate(1deg);
            }
            25% {
                bottom: 200px;
                left: 48%;
                transform: translateX(-50%) scale(1.02) rotate(-0.5deg);
                opacity: 1;
            }
            45% {
                bottom: 300px;
                left: 51%;
                transform: translateX(-50%) scale(0.98) rotate(0.5deg);
                opacity: 1;
            }
            65% {
                bottom: 400px;
                left: 49%;
                transform: translateX(-50%) scale(0.95) rotate(-0.3deg);
                opacity: 0.9;
            }
            80% {
                bottom: 500px;
                left: 50%;
                transform: translateX(-50%) scale(0.8) rotate(0deg);
                opacity: 0.6;
            }
            95% {
                bottom: 65%; /* 飞向思考指示器位置 (35% + 30%) */
                left: 50%;
                transform: translateX(-50%) scale(0.4) rotate(0deg);
                opacity: 0.2;
            }
            100% {
                bottom: 65%;
                left: 50%;
                transform: translateX(-50%) scale(0.1) rotate(0deg);
                opacity: 0;
            }
        }

        /* 添加粒子效果容器 */
        .bubble-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        /* 粒子效果 - 增强版 */
        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: var(--accent-primary);
            border-radius: 50%;
            opacity: 0;
            box-shadow: 0 0 6px var(--accent-primary-glow);
        }

        .particle.type-1 {
            animation: particle-float-1 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .particle.type-2 {
            animation: particle-float-2 2.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .particle.type-3 {
            animation: particle-float-3 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        @keyframes particle-float-1 {
            0% {
                opacity: 0.8;
                transform: scale(1) translateY(0px) translateX(0px);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.2) translateY(-50px) translateX(20px);
            }
            100% {
                opacity: 0;
                transform: scale(0.3) translateY(-120px) translateX(40px);
            }
        }

        @keyframes particle-float-2 {
            0% {
                opacity: 0.8;
                transform: scale(1) translateY(0px) translateX(0px);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.1) translateY(-60px) translateX(-25px);
            }
            100% {
                opacity: 0;
                transform: scale(0.2) translateY(-140px) translateX(-50px);
            }
        }

        @keyframes particle-float-3 {
            0% {
                opacity: 0.8;
                transform: scale(1) translateY(0px) translateX(0px);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.3) translateY(-40px) translateX(10px);
            }
            100% {
                opacity: 0;
                transform: scale(0.1) translateY(-100px) translateX(15px);
            }
        }
        /* --- 全屏按钮的核心改造 --- */
        #expand-input-btn {
            /* 为按钮自身也添加过渡效果 */
            transition: all 0.4s ease-in-out;
        }



        #view-ai-workflow.fullscreen-active #expand-input-btn {
            /* 全屏时，将按钮定位到右上角 */
            position: absolute;
            top: 24px;
            right: 24px;
            z-index: 1010; /* 确保在最上层 */
        }

        /* 全屏时，切换图标的显示/隐藏 */
        #view-ai-workflow.fullscreen-active .icon-expand {
            display: none;
        }
        #view-ai-workflow.fullscreen-active .icon-collapse {
            display: block;
        }

        /* 新增：默认只显示“展开”图标，隐藏“收起”图标 */
        .icon-collapse {
            display: none;
        }
        .icon-expand {
            display: block;
        }
        #prompt-input-wrapper > #expand-input-btn,
        #prompt-input-wrapper > #send-prompt-btn {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
            color: var(--text-secondary);
        }

        /* 2. 统一并美化按钮里的SVG图标 */
        #prompt-input-wrapper > button svg {
            stroke: currentColor; /* 让图标颜色继承按钮的color属性 */
            stroke-width: 2.5;    /* 让线条稍粗一点，更清晰 */
            transition: transform 0.2s ease;
        }

        /* 3. 优化鼠标悬停/聚焦效果 */
        #prompt-input-wrapper > button:hover svg {
            color: var(--text-primary);
            transform: scale(1.1); /* 轻微放大，增加动感 */
        }

        /* 4. 修复全屏时“收起”按钮被遮挡的问题 */
        #view-ai-workflow.fullscreen-active #expand-input-btn {
            z-index: 10; /* 确保它在最上层，不会被文本框遮挡 */
        }
    </style>
</head>
<body>
    <!-- Login View -->
    <div id="login-view">
        <div id="login-form-container" class="form-container">
            <h2>登录 Zygo IDE</h2>
            <input type="text" id="login-username" placeholder="用户名" value="testuser">
            <div class="password-wrapper">
                <input type="password" id="login-password" placeholder="密码" value="password123">
                <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('login-password', this)">
                    <svg class="eye-open" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    <svg class="eye-closed" style="display: none;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/><path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"/><path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"/><line x1="2" y1="2" x2="22" y2="22"/></svg>
                </button>
            </div>
            <button id="login-button">登录</button>
            <p id="login-error" class="form-message error"></p>
            <p class="form-toggle" onclick="toggleForms()">没有账户？注册一个</p>
        </div>
        <div id="register-form-container" class="form-container" style="display: none;">
            <h2>注册新用户</h2>
            <input type="text" id="register-username" placeholder="设置用户名">
            <div class="password-wrapper">
                <input type="password" id="register-password" placeholder="设置密码">
                <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('register-password', this)">
                    <svg class="eye-open" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    <svg class="eye-closed" style="display: none;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/><path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"/><path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"/><line x1="2" y1="2" x2="22" y2="22"/></svg>
                </button>
            </div>
            <button id="register-button">注册</button>
            <p id="register-message" class="form-message"></p>
            <p class="form-toggle" onclick="toggleForms()">已有账户？去登录</p>
        </div>
    </div>

    <!-- Main IDE View -->
    <div id="ide-view" class="ide-container" style="display: none;">
        <div id="activity-bar">
            <div class="activity-group">
                <button id="btn-activity-ai" class="activity-btn active" title="AI 工作流"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/><path d="M5 3v4"/><path d="M19 17v4"/><path d="M3 5h4"/><path d="M17 19h4"/></svg></button>
                <button id="btn-activity-files" class="activity-btn" title="文件浏览器"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><line x1="10" y1="9" x2="8" y2="9"/></svg></button>
                <button id="btn-activity-devices" class="activity-btn" title="设备管理器"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2"/><path d="M6 12h4"/><path d="M14 12h4"/><path d="M6 16h12"/></svg></button>
            </div>
            <div class="activity-group">
                <button id="btn-theme-toggle" class="activity-btn" title="切换主题"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="theme-icon-sun"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="theme-icon-moon" style="display: none;"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg></button>
                <button id="btn-settings" class="activity-btn" title="用户设置"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg></button>
            </div>
        </div>

        <div class="content-container">
            <div id="main-view">
                <div id="view-ai-workflow" class="view active">
                    <div id="prompt-animation-container"></div>

                    <div id="initial-prompt-container">
                        <div id="welcome-greeting-wrapper">
                            <h1 id="welcome-greeting"></h1>
                        </div>

                        <div id="thinking-indicator" style="display: none;">
                            <div class="thinking-content">
                                <div class="thinking-icon-container">
                                    <svg class="logo-icon" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="var(--accent-primary)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/>
                                        <path d="M5 3v4"/>
                                        <path d="M19 17v4"/>
                                        <path d="M3 5h4"/>
                                        <path d="M17 19h4"/>
                                    </svg>
                                    <div class="thinking-particles">
                                        <div class="thinking-particle"></div>
                                        <div class="thinking-particle"></div>
                                        <div class="thinking-particle"></div>
                                        <div class="thinking-particle"></div>
                                        <div class="thinking-particle"></div>
                                    </div>
                                </div>
                                <div class="thinking-text">
                                    <span class="thinking-message">AI正在思考</span>
                                    <div class="thinking-dots">
                                        <span class="dot">.</span>
                                        <span class="dot">.</span>
                                        <span class="dot">.</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="prompt-input-wrapper">
                             <textarea id="raw-text-input" rows="1" placeholder="描述您的项目想法..."></textarea>
                            <button id="expand-input-btn" title="展开/收起">
                                <svg class="icon-expand" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/></svg>
                                <svg class="icon-collapse" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"/></svg>
                            </button>
                             <button id="send-prompt-btn" title="发送" disabled>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="19" x2="12" y2="5"></line><polyline points="5 12 12 5 19 12"></polyline></svg>
                             </button>
                        </div>
                    </div>
                    <div id="workflow-stream-container"></div>
                    <div id="deployment-actions-container" style="padding: 20px; display: none; justify-content: center; gap: 20px;"></div>
                </div>

                <div id="view-file-explorer" class="view">
                    <div id="file-tree-panel">
                        <div id="file-tree-header">
                            <h3>文件浏览器</h3>
                            <div style="display: flex; gap: 8px;">
                                <button id="flash-firmware-btn" class="secondary" title="烧录固件到设备" style="display: inline-flex;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/></svg>
                                </button>
                                <button id="load-cloud-project-btn" class="secondary" title="加载云端项目">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M6.5 17.5C4.01 17.5 2 15.49 2 13s2.01-4.5 4.5-4.5c.34 0 .68.04 1 .11C8.58 6.07 10.69 4.5 13.5 4.5c3.59 0 6.5 2.91 6.5 6.5 0 .79-.14 1.55-.4 2.26.78.4 1.4 1.18 1.4 2.24 0 1.38-1.12 2.5-2.5 2.5h-12Z"/></svg>
                                </button>
                                <button id="open-local-folder-btn" class="secondary" title="打开本地文件夹">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path></svg>
                                </button>
                            </div>
                        </div>
                        <div id="file-tree-container"></div>
                    </div>
                    <div id="sidebar-resizer"></div>
                    <div id="editor-area-container" style="flex-grow: 1; display: flex; flex-direction: column;">
                        <div id="editor-welcome-message" style="display: flex; flex-grow: 1; justify-content: center; align-items: center; text-align: center; color: var(--text-secondary);">
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" style="stroke: var(--border-color);"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/><path d="M5 3v4"/><path d="M19 17v4"/><path d="M3 5h4"/><path d="M17 19h4"/></svg>
                                <h2 style="color: var(--text-primary); margin: 15px 0 5px;">Zygo AI IDE</h2>
                                <p>请从左侧选择一个文件进行查看和编辑</p>
                            </div>
                        </div>
                        <div id="code-editor-panel" style="display: none; flex-grow: 1; flex-direction: column;">
                            <div id="code-editor-header">
                                <span id="editing-file-name"></span>
                                <button id="save-file-btn">保存</button>
                            </div>
                            <div id="monaco-editor-container" style="flex-grow: 1;"></div>
                        </div>
                    </div>
                </div>

                <div id="view-device-manager" class="view">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2>设备管理器</h2>
                        <button id="add-device-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                            注册新设备
                        </button>
                    </div>
                    <div id="device-grid" class="device-grid"></div>
                </div>
            </div>

            <div id="panel-resizer"></div>

            <div id="problems-panel">
                <div id="problems-panel-header">
                    <span>问题</span>
                    <button onclick="closeProblemsPanel()" style="background: none; border: none; cursor: pointer; color: var(--text-secondary); padding: 4px; box-shadow: none; border-radius: 4px; transition: all 0.2s ease;"
                            onmouseover="this.style.backgroundColor='var(--bg-surface-hover)'; this.style.color='var(--text-primary)';"
                            onmouseout="this.style.backgroundColor='transparent'; this.style.color='var(--text-secondary)';">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
                <div id="problems-panel-content">
                </div>
            </div>

            <div id="status-bar">
                <div id="status-workflow" class="status-item">
                     <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0-4.4-3.6-8-8-8S5 5.6 5 10c0 4.4 3.6 8 8 8"/><path d="M12 2v4"/><path d="m6.4 7.4-3 3"/><path d="M2 12h4"/><path d="m6.4 16.6-3-3"/><path d="M12 18v4"/><path d="m17.6 16.6 3-3"/><path d="M22 12h-4"/><path d="m17.6 7.4 3 3"/></svg>
                    <span>未开始</span>
                </div>
                 <div id="status-device" class="status-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2"/><path d="M6 12h4"/></svg>
                    <span>无设备</span>
                </div>
                <div id="status-problems"
                     class="status-item"
                     style="display:none;cursor:pointer;"
                     title="切换问题面板"
                     onclick="toggleProblemsPanel()">
                </div>
                <div id="status-user-actions">
                    <div id="status-user" class="status-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>
                        <span id="username-display"></span>
                    </div>
                    <button id="logout-button" class="activity-btn" style="width: auto; height: auto;" title="登出">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/><polyline points="16 17 21 12 16 7"/><line x1="21" y1="12" x2="9" y2="12"/></svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Dialogs -->
    <dialog id="confirmation-dialog">
        <div class="dialog-content">
            <h2>请确认项目信息</h2>
            <p>AI已为您解析需求，请检查并补全信息后开始构建。</p>
            <form id="confirmation-form" style="display: flex; flex-direction: column; gap: 15px;"></form>
            <!-- 【核心修改】调整了对话框按钮 -->
            <div class="dialog-actions">
                <button type="button" id="save-template-btn" class="secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>
                    另存为模板
                </button>
                <button type="button" class="secondary" onclick="document.getElementById('confirmation-dialog').close()">取消</button>
                <button type="submit" form="confirmation-form">确认并开始构建</button>
            </div>
        </div>
    </dialog>

    <dialog id="device-dialog">
        <div class="dialog-content">
            <h2 id="device-dialog-title">注册新设备</h2>
            <form id="device-form" style="display: flex; flex-direction: column; gap: 15px;">
                <input type="hidden" id="device-internal-id">

                <div class="form-group">
                    <label for="device-nickname">设备昵称</label>
                    <input type="text" id="device-nickname" placeholder="例如: 客厅的温湿度计" required>
                </div>

                <div class="form-group">
                    <label for="device-board">开发板型号</label>
                    <input type="text" id="device-board" placeholder="例如: esp32dev" required>
                </div>

                <div class="form-group">
                    <label for="cloud-platform">云平台</label>
                    <select id="cloud-platform">
                        <option value="tuya" selected>涂鸦智能 (Tuya)</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="cloud-product-id">产品ID</label>
                    <input type="text" id="cloud-product-id" placeholder="Product ID">
                </div>

                <div class="form-group">
                    <label for="cloud-device-id">设备ID</label>
                    <input type="text" id="cloud-device-id" placeholder="Device ID">
                </div>

                <div class="form-group">
                    <label for="cloud-device-secret">设备密钥</label>
                    <div class="password-wrapper">
                        <input type="password" id="cloud-device-secret" placeholder="Device Secret">
                        <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('cloud-device-secret', this)">
                            <svg class="eye-open" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                            <svg class="eye-closed" style="display: none;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/><path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"/><path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"/><line x1="2" y1="2" x2="22" y2="22"/></svg>
                        </button>
                    </div>
                </div>
                <div id="device-peripherals-container" style="margin-top: 10px; border-top: 1px solid var(--border-color); padding-top: 15px;"></div>
                <button type="button" class="secondary" onclick="openSmartPeripheralDialog()">+ 智能添加外设</button>
                <p id="device-form-error" class="form-message error"></p>
            </form>
             <div class="dialog-actions">
                <button type="button" class="secondary" onclick="document.getElementById('device-dialog').close()">取消</button>
                <button type="submit" id="device-form-submit-btn" form="device-form">注册</button>
            </div>
        </div>
    </dialog>

    <!-- 智能外设配置对话框 -->
    <dialog id="smart-peripheral-dialog">
        <div class="dialog-content">
            <h2>智能外设配置</h2>
            <form id="smart-peripheral-form" style="display: flex; flex-direction: column; gap: 15px;">

                <!-- 第1步：选择核心功能 -->
                <div class="step-container" id="step-1">
                    <h3 style="color: var(--primary-color); margin-bottom: 10px;">第1步：选择核心功能</h3>
                    <p style="color: var(--text-secondary); font-size: 0.9em; margin-bottom: 15px;">
                        请首先选择您要添加的硬件组件所要实现的核心功能
                    </p>
                    <div class="form-group">
                        <label for="peripheral-function">您想实现什么功能？</label>
                        <select id="peripheral-function" required>
                            <option value="">-- 请选择功能 --</option>
                            <option value="LIGHT_SENSING">光照检测</option>
                            <option value="TEMP_HUMIDITY_SENSING">温湿度检测</option>
                            <option value="MOTION_DETECTION">运动检测 (人体红外)</option>
                            <option value="DISTANCE_MEASUREMENT">距离测量 (超声波)</option>
                            <option value="ATTITUDE_DETECTION">姿态检测 (陀螺仪/加速度计)</option>
                            <option value="PRESSURE_SENSING">压力检测</option>
                            <option value="TEMPERATURE_SENSING">温度检测</option>
                            <option value="RELAY_SWITCH">继电器开关</option>
                            <option value="BUZZER">蜂鸣器</option>
                            <option value="LED_CONTROL">LED控制</option>
                            <option value="DISPLAY">显示屏</option>
                            <option value="OTHER">其他</option>
                        </select>
                    </div>
                </div>

                <!-- 第2步：选择实现方式 -->
                <div class="step-container" id="step-2" style="display: none;">
                    <h3 style="color: var(--primary-color); margin-bottom: 10px;">第2步：选择实现方式</h3>
                    <p style="color: var(--text-secondary); font-size: 0.9em; margin-bottom: 15px;">
                        选择您要使用的实现方式
                    </p>
                    <div class="form-group">
                        <label for="implementation-type">实现方式</label>
                        <select id="implementation-type" required>
                            <option value="">-- 请选择实现方式 --</option>
                            <option value="SPECIFIC_MODEL">使用特定型号的模块</option>
                            <option value="GENERIC_COMPONENT">使用通用电子元件</option>
                        </select>
                    </div>
                </div>

                <!-- 第3步：动态配置 -->
                <div class="step-container" id="step-3" style="display: none;">
                    <h3 style="color: var(--primary-color); margin-bottom: 10px;">第3步：具体配置</h3>
                    <div id="dynamic-config-container">
                        <!-- 动态生成的配置内容 -->
                    </div>
                </div>

                <p id="smart-peripheral-error" class="form-message error"></p>
            </form>
            <div class="dialog-actions">
                <button type="button" class="secondary" onclick="document.getElementById('smart-peripheral-dialog').close()">取消</button>
                <button type="button" id="smart-peripheral-prev-btn" class="secondary" onclick="smartPeripheralPrevStep()">上一步</button>
                <button type="button" id="smart-peripheral-next-btn" onclick="smartPeripheralNextStep()">下一步</button>
                <button type="button" id="smart-peripheral-submit-btn" onclick="submitSmartPeripheral()">添加外设</button>
            </div>
        </div>
    </dialog>

    <dialog id="settings-dialog">
        <div class="dialog-content">
            <h2>用户设置</h2>
            <p>在这里配置您的个人偏好，例如Wi-Fi凭证，用于自动写入设备代码。</p>
            <form id="settings-form" style="display: flex; flex-direction: column; gap: 15px;">
                 <input type="text" id="wifi-ssid" placeholder="Wi-Fi 名称 (SSID)">
                 <div class="password-wrapper">
                     <input type="password" id="wifi-password" placeholder="Wi-Fi 密码">
                     <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('wifi-password', this)">
                         <svg class="eye-open" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                         <svg class="eye-closed" style="display: none;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/><path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"/><path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"/><line x1="2" y1="2" x2="22" y2="22"/></svg>
                     </button>
                 </div>
                 <p id="settings-message" class="form-message"></p>
            </form>
             <div class="dialog-actions">
                <button type="button" class="secondary" onclick="document.getElementById('settings-dialog').close()">取消</button>
                <button type="submit" id="settings-form-submit-btn" form="settings-form">保存设置</button>
            </div>
        </div>
    </dialog>

    <dialog id="project-loader-dialog">
        <div class="dialog-content">
            <h2>加载云端项目</h2>
            <p>您可以加载历史构建记录以查看文件，或使用项目模板开始一次新的构建。</p>
            <ul id="project-list"></ul>
        </div>
         <div class="dialog-actions">
            <button type="button" class="secondary" onclick="document.getElementById('project-loader-dialog').close()">关闭</button>
        </div>
    </dialog>

    <dialog id="mqtt-config-dialog">
        <div class="dialog-content">
            <h2>🔗 MQTT配置</h2>
            <form id="mqtt-config-form" style="display: flex; flex-direction: column; gap: 0;">
                <input type="hidden" id="mqtt-device-id">

                <!-- 连接配置 -->
                <div class="mqtt-config-section">
                    <h3>🌐 连接设置</h3>
                    <div class="mqtt-form-row">
                        <div class="mqtt-form-group">
                            <label for="mqtt-broker-host">Broker地址</label>
                            <div class="mqtt-input-with-icon">
                                <span class="mqtt-input-icon">🖥️</span>
                                <input type="text" id="mqtt-broker-host" placeholder="例如: localhost 或 *************" required>
                            </div>
                            <div class="mqtt-topics-help">
                                💡 常用地址：localhost、127.0.0.1、*************<br>
                                ⚠️ 只需要主机名或IP，不要加 mqtt:// 前缀
                            </div>
                        </div>
                        <div class="mqtt-form-group" style="flex: 0 0 120px;">
                            <label for="mqtt-broker-port">端口</label>
                            <div class="mqtt-input-with-icon">
                                <span class="mqtt-input-icon">🔌</span>
                                <input type="number" id="mqtt-broker-port" value="1883" min="1" max="65535" required>
                            </div>
                        </div>
                    </div>
                    <div class="mqtt-form-row">
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <button type="button" class="secondary" onclick="setMqttPreset('localhost')" style="font-size: 0.8em; padding: 4px 8px;">localhost</button>
                            <button type="button" class="secondary" onclick="setMqttPreset('127.0.0.1')" style="font-size: 0.8em; padding: 4px 8px;">127.0.0.1</button>
                            <button type="button" class="secondary" onclick="setMqttPreset('*************')" style="font-size: 0.8em; padding: 4px 8px;">*************</button>
                            <button type="button" class="secondary" onclick="setMqttPreset('test.mosquitto.org')" style="font-size: 0.8em; padding: 4px 8px;">公共测试服务器</button>
                        </div>
                    </div>
                </div>

                <!-- 认证配置 -->
                <div class="mqtt-config-section">
                    <h3>🔐 认证信息</h3>
                    <div class="mqtt-form-row">
                        <div class="mqtt-form-group">
                            <label for="mqtt-username">用户名 <span class="label-optional">(可选)</span></label>
                            <div class="mqtt-input-with-icon">
                                <span class="mqtt-input-icon">👤</span>
                                <input type="text" id="mqtt-username" placeholder="MQTT用户名">
                            </div>
                        </div>
                        <div class="mqtt-form-group">
                            <label for="mqtt-password">密码 <span class="label-optional">(可选)</span></label>
                            <div class="mqtt-input-with-icon password-wrapper">
                                <span class="mqtt-input-icon">🔑</span>
                                <input type="password" id="mqtt-password" placeholder="MQTT密码">
                                <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('mqtt-password', this)" style="right: 12px;">
                                    <svg class="eye-open" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                                    <svg class="eye-closed" style="display: none;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/><path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"/><path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"/><line x1="2" y1="2" x2="22" y2="22"/></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="mqtt-form-group">
                        <label for="mqtt-client-id">客户端ID <span class="label-optional">(可选)</span></label>
                        <div class="mqtt-input-with-icon">
                            <span class="mqtt-input-icon">🏷️</span>
                            <input type="text" id="mqtt-client-id" placeholder="留空将自动生成">
                        </div>
                    </div>
                </div>

                <!-- 订阅配置 -->
                <div class="mqtt-config-section">
                    <h3>📡 订阅主题</h3>
                    <div class="mqtt-topics-container" id="mqtt-topics-container">
                        <!-- 动态生成的主题输入框将在这里 -->
                    </div>
                    <div class="mqtt-topics-actions">
                        <button type="button" class="secondary" onclick="addMqttTopicInput()">➕ 添加主题</button>
                        <button type="button" class="secondary" onclick="addCommonMqttTopics()">📋 添加常用主题</button>
                    </div>
                    <div class="mqtt-topics-help">
                        💡 支持MQTT通配符：+ (单级) 和 # (多级)<br>
                        例如：sensor/+/temperature 或 device/#
                    </div>
                </div>

                <!-- 监控开关 -->
                <div class="mqtt-monitoring-toggle">
                    <input type="checkbox" id="mqtt-monitoring-enabled">
                    <label for="mqtt-monitoring-enabled">🔍 启用MQTT监控</label>
                </div>

                <div id="mqtt-config-error" style="color: var(--accent-error); font-size: 0.9em; margin-top: 10px;"></div>
            </form>
        </div>
        <div class="dialog-actions">
            <button type="button" class="secondary" onclick="document.getElementById('mqtt-config-dialog').close()">❌ 取消</button>
            <button type="button" onclick="testMqttConnection()" id="mqtt-test-btn">🔍 测试连接</button>
            <button type="submit" form="mqtt-config-form" onclick="handleMqttConfigSubmit(event)">💾 保存配置</button>
        </div>
    </dialog>

<script>
// 函数已在head中定义，这里只需要验证它们是否可用
console.log('Body script - Functions available:', typeof toggleForms, typeof togglePasswordVisibility);

    document.addEventListener('DOMContentLoaded', () => {
    // --- 全局错误处理 ---
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message) {
            const errorMsg = event.error.message;
            if (errorMsg.includes('getModifierState') ||
                errorMsg.includes('keyboardEvent') ||
                errorMsg.includes('StandardKeyboardEvent')) {
                console.warn('Caught Monaco Editor keyboard event error:', event.error);
                event.preventDefault(); // 阻止错误冒泡到控制台
                return true;
            }
        }
    });

    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message) {
            const errorMsg = event.reason.message;
            if (errorMsg.includes('getModifierState') ||
                errorMsg.includes('keyboardEvent') ||
                errorMsg.includes('StandardKeyboardEvent')) {
                console.warn('Caught Monaco Editor promise rejection:', event.reason);
                event.preventDefault();
                return true;
            }
        }
    });

    // --- State Management & Globals (unchanged) ---
    let currentWorkflowId = null;
    // accessToken 现在是全局变量
    let userDevices = [];
    let statusInterval = null;
    let currentEditingPath = null;
    let lastRenderedStepState = {};
    const API_BASE_URL = '/api/v1';
    let isLocalProject = false;
    let syncedLocalProjectId = null;
    // 新增变量
    let localFileHandles = new Map();
    // 移到外部声明，并移除初始的 null 赋值
    let monacoEditor;
    let problemsFilter = 'all';
    // --- DOM Elements ---
    let loginView, ideView, workflowStreamContainer, initialPromptContainer, deviceGrid;

    // ===================================================================
    // Utility Functions (unchanged)
    // ===================================================================

    // ===================================================================
    // Utility Functions
    // ===================================================================

    function setupSidebarResizer() {
        const resizer = document.getElementById('sidebar-resizer');
        const sidebar = document.getElementById('file-tree-panel');

        function startResize(e) {
            e.preventDefault();
            const startX = e.clientX;
            const startWidth = sidebar.offsetWidth;

            function doResize(e) {
                const newWidth = startWidth + e.clientX - startX;
                if (newWidth > 200 && newWidth < window.innerWidth * 0.5) { // 设置最小/最大宽度
                    sidebar.style.width = newWidth + 'px';
                }
            }

            function stopResize() {
                window.removeEventListener('mousemove', doResize);
                window.removeEventListener('mouseup', stopResize);
                document.body.style.cursor = '';
            }

            window.addEventListener('mousemove', doResize);
            window.addEventListener('mouseup', stopResize);
            document.body.style.cursor = 'ew-resize';
        }

        resizer.addEventListener('mousedown', startResize);
    }

    function closeProblemsPanel() {
        const panel = document.getElementById('problems-panel');
        const resizer = document.getElementById('panel-resizer');

        // 直接设置为 none，不依赖其他逻辑
        panel.style.display = 'none';
        resizer.style.display = 'none';

        // 可选：重置过滤器状态
        problemsFilter = 'all';
    }

    function toggleProblemsPanel(forceShow = null) {
        const panel = document.getElementById('problems-panel');
        const resizer = document.getElementById('panel-resizer');

        // 如果有强制显示参数，直接使用
        if (forceShow !== null) {
            panel.style.display = forceShow ? 'flex' : 'none';
            resizer.style.display = forceShow ? 'block' : 'none';
            return;
        }

        // 否则切换显示状态
        // 使用 style.display 而不是 getComputedStyle，因为我们总是显式设置 display
        const isCurrentlyHidden = panel.style.display === 'none' || !panel.style.display;

        panel.style.display = isCurrentlyHidden ? 'flex' : 'none';
        resizer.style.display = isCurrentlyHidden ? 'block' : 'none';
    }

    // 【核心新增】用于处理部署按钮点击的函数
    async function postDeploymentAction(action) {
        if (!currentWorkflowId) return;

        const container = document.getElementById('deployment-actions-container');
        container.innerHTML = `<span style="color: var(--text-secondary);">正在恢复: ${action}...</span>`;

        try {
            await fetchApi(`/workflows/${currentWorkflowId}/actions`, 'POST', { action: action }, 202);
            // API调用成功后，轮询将自动接管后续的状态更新
        } catch (error) {
            alert(`执行操作失败: ${error.message}`);
            // 如果失败，可能需要重新渲染按钮
            checkStatus();
        }
    }

    function escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return unsafe;
        return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
    }
    function updateMessage(elementId, text, isError = false) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
            element.className = 'form-message ' + (isError ? 'error' : 'success');
        }
    }


    function defineAndSetMonacoTheme() {
        // 读取CSS变量来定义主题颜色
        const styles = getComputedStyle(document.documentElement);
        const isDark = !document.documentElement.hasAttribute('data-theme');

        const themeData = {
            base: isDark ? 'vs-dark' : 'vs',
            inherit: true,
            rules: [
                // 你可以在这里添加更精细的规则
            ],
            colors: {
                'editor.background': styles.getPropertyValue('--bg-surface').trim(),
                'editor.foreground': styles.getPropertyValue('--text-primary').trim(),
                'editorCursor.foreground': styles.getPropertyValue('--accent-primary').trim(),
                'editor.lineHighlightBackground': styles.getPropertyValue('--bg-surface-hover').trim(),
                'editor.selectionBackground': styles.getPropertyValue('--scrollbar-thumb').trim(),
                'editorWidget.background': styles.getPropertyValue('--bg-surface').trim(),
                'editorWidget.border': styles.getPropertyValue('--border-color').trim()
            }
        };

        monaco.editor.defineTheme('custom-theme', themeData);
        monaco.editor.setTheme('custom-theme');
    }

    async function fetchApi(endpoint, method = 'GET', body = null, expectedStatus) {
        const options = { method, headers: { 'Authorization': `Bearer ${accessToken}` } };
        if (body) {
            options.headers['Content-Type'] = 'application/json';
            options.body = JSON.stringify(body);
        }
        const response = await fetch(API_BASE_URL + endpoint, options);
        const responseText = await response.text();
        const isSuccess = expectedStatus ? response.status === expectedStatus : response.ok;
        if (!isSuccess) {
            let errorMsg = `请求失败，状态码: ${response.status}`;
            let errorData = null;
            try {
                errorData = JSON.parse(responseText);
                errorMsg = errorData.error || errorMsg;
            } catch (e) {}

            // 创建带有响应数据的错误对象
            const error = new Error(errorMsg);
            error.response = response;
            error.responseData = errorData;
            throw error;
        }
        try { return JSON.parse(responseText); } catch (e) { return {}; }
    }

    // ===================================================================
    // Project Confirmation Dialog Logic (Refactored for Template Management)
    // ===================================================================
    function buildConfirmationFormHtml(data) {
        // This function remains the same as the previous version
        let formHtml = `
            <label>项目名称:</label>
            <input type="text" id="final-project-name" value="${escapeHtml(data.project_name || '')}" required>
            <label>项目总描述:</label>
            <textarea id="final-project-description" rows="3">${escapeHtml(data.project_description || '')}</textarea>
            <h4 style="margin-top: 15px; margin-bottom: 5px; border-top: 1px solid var(--border-color); padding-top: 15px;">设备任务列表</h4>
            <div id="device-tasks-container">
        `;
        (data.device_tasks || []).forEach((task, index) => {
            formHtml += createPopulatedDeviceCardHtml(task, index);
        });
        formHtml += `</div>
            <button type="button" id="add-device-task-btn" class="secondary" style="margin-top: 15px; width: 100%;">+ 添加一个新设备任务</button>
            <div style="display: flex; align-items: center; gap: 15px; margin-top: 15px; border-top: 1px solid var(--border-color); padding-top: 15px;">
                <h4>设备间通信计划</h4>
                <button type="button" class="secondary" onclick="refreshCommunicationPlan()" title="根据上方修改重新分析通信" style="padding: 5px 10px; font-size: 0.8em;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 2v6h6"/><path d="M21 12A9 9 0 0 0 6 5.3L3 8"/><path d="M21 22v-6h-6"/><path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"/></svg>
                    刷新
                </button>
            </div>
            <div id="communication-plan-container" style="padding: 10px 0;">`;

        if (data.inter_device_communication && data.inter_device_communication.length > 0) {
             formHtml += data.inter_device_communication.map(comm => `
                <div class="data-flow-line">
                    <div class="flow-node source">${escapeHtml(comm.source_device_role)}</div>
                    <div class="flow-path">
                        <div class="flow-arrow"></div>
                        <div class="flow-label">[${escapeHtml(comm.protocol)}] ${escapeHtml(comm.data_description)}</div>
                    </div>
                    <div class="flow-node target">${escapeHtml(comm.target_device_role)}</div>
                </div>
             `).join('');
        } else {
            formHtml += `<p style="color: var(--text-secondary);">AI未检测到设备间通信。</p>`;
        }
        formHtml += `</div>`;
        return formHtml;
    }

    function createPopulatedDeviceCardHtml(task, index) {
        // This function remains the same as the previous version
        return `
        <div class="device-task-card">
            <div class="device-card-header">
                <h5>设备角色: <input type="text" class="device-role" value="${escapeHtml(task.device_role || '')}"></h5>
                <button type="button" class="remove-task-btn" onclick="this.closest('.device-task-card').remove()" title="删除此设备任务">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg>
                </button>
            </div>
            <div class="device-card-content">
                <div>
                    <label>目标硬件:</label>
                    <select class="internal-device-id" required>
                        <option value="">-- 请选择已注册的设备 --</option>
                        ${userDevices.map(d => `<option value="${d.internal_device_id}" ${d.internal_device_id === task.internal_device_id ? 'selected' : ''}>${d.nickname} (${d.board_model})</option>`).join('')}
                    </select>
                </div>
                <div>
                    <label>设备职责描述:</label>
                    <textarea class="device-description" rows="3">${escapeHtml(task.description || '')}</textarea>
                </div>
                <div class="peripheral-wrapper">
                    <div class="peripheral-container" data-task-index="${index}">
                        <label>外设配置:</label>
                        <div style="display: grid; grid-template-columns: 1.5fr 1.5fr 2.5fr auto; gap: 10px; color: var(--text-secondary); font-size: 0.9em; margin-bottom: 5px;">
                            <span>名称</span><span>型号</span><span>引脚配置</span><span></span>
                        </div>
                        <!-- 外设将通过 addPeripheralToTaskForm 函数动态添加 -->
                    </div>
                    <button type="button" class="secondary" onclick="openSmartPeripheralDialogForTask(${index})" style="margin-top:10px;">+ 智能添加外设</button>
                </div>
            </div>
        </div>
        `;
    }

    function createNewDeviceCardHtml(index) {
        // This function remains the same as the previous version
        return `
        <div class="device-task-card">
            <div class="device-card-header">
                <h5>设备角色: <input type="text" class="device-role" value="新设备 ${index + 1}"></h5>
                <button type="button" class="remove-task-btn" onclick="this.closest('.device-task-card').remove()" title="删除此设备任务">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg>
                </button>
            </div>
            <div class="device-card-content">
                <div>
                    <label>目标硬件:</label>
                    <select class="internal-device-id" required>
                        <option value="">-- 请选择已注册的设备 --</option>
                        ${userDevices.map(d => `<option value="${d.internal_device_id}">${d.nickname} (${d.board_model})</option>`).join('')}
                    </select>
                </div>
                <div>
                    <label>设备职责描述:</label>
                    <textarea class="device-description" rows="3" placeholder="描述这个新设备需要做什么"></textarea>
                </div>
                <div class="peripheral-wrapper">
                    <div class="peripheral-container" data-task-index="${index}">
                        <label>外设配置:</label>
                    </div>
                    <button type="button" class="secondary" onclick="openSmartPeripheralDialogForTask(${index})" style="margin-top:10px;">+ 智能添加外设</button>
                </div>
            </div>
        </div>
        `;
    }

    function collectPayloadFromForm() {
        // This function remains the same as the previous version
        const finalPayload = {
            project_name: document.getElementById('final-project-name').value,
            project_description: document.getElementById('final-project-description').value,
            device_tasks: [],
            inter_device_communication: []
        };

        document.querySelectorAll('.device-task-card').forEach(card => {
            const task = {
                device_role: card.querySelector('.device-role').value,
                internal_device_id: card.querySelector('.internal-device-id').value,
                description: card.querySelector('.device-description').value,
                peripherals: []
            };
            card.querySelectorAll('.peripheral-item').forEach(p_item => {
                // 从 data-config 属性读取外设配置
                const configString = p_item.dataset.config;
                if (configString) {
                    try {
                        const peripheralConfig = JSON.parse(configString);
                        // 从配置中提取外设信息
                        task.peripherals.push({
                            name: peripheralConfig.name,
                            model: peripheralConfig.model,
                            interface: peripheralConfig.interface,
                            function: peripheralConfig.function,
                            implementationType: peripheralConfig.implementationType,
                            pins: peripheralConfig.pins || [],
                            config: peripheralConfig.config || {},
                            description: peripheralConfig.description
                        });
                    } catch (e) {
                        console.error('解析外设配置失败:', e, configString);
                    }
                }
            });
            finalPayload.device_tasks.push(task);
        });

        document.querySelectorAll('.data-flow-line').forEach(line => {
            const source = line.querySelector('.flow-node.source').textContent;
            const target = line.querySelector('.flow-node.target').textContent;
            const label = line.querySelector('.flow-label').textContent;
            const protocolMatch = label.match(/\[(.*?)\]/);
            const protocol = protocolMatch ? protocolMatch[1] : 'MQTT';
            const data_description = label.replace(/\[.*?\]\s*/, '');
            finalPayload.inter_device_communication.push({
                source_device_role: source,
                target_device_role: target,
                data_description: data_description,
                protocol: protocol
            });
        });

        return finalPayload;
    }

    function renderCommunicationPlan(plan) {
        // This function remains the same as the previous version
        const container = document.getElementById('communication-plan-container');
        if (!container) return;
        let html = '';
        const comms = plan.inter_device_communication || [];
        if (comms.length > 0) {
            html = comms.map(comm => `
                <div class="data-flow-line">
                    <div class="flow-node source">${escapeHtml(comm.source_device_role)}</div>
                    <div class="flow-path"><div class="flow-arrow"></div><div class="flow-label">[${escapeHtml(comm.protocol)}] ${escapeHtml(comm.data_description)}</div></div>
                    <div class="flow-node target">${escapeHtml(comm.target_device_role)}</div>
                </div>
            `).join('');
        } else {
            html = `<p style="color: var(--text-secondary);">AI未检测到设备间通信。</p>`;
        }
        container.innerHTML = html;
    }

    async function refreshCommunicationPlan() {
        // This function remains the same as the previous version
        const deviceTasks = [];
        document.querySelectorAll('.device-task-card').forEach(card => {
            const selectElement = card.querySelector('.internal-device-id');
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            const nickname = selectedOption.text.split(' (')[0];
            deviceTasks.push({
                device_role: card.querySelector('.device-role').value,
                nickname: nickname,
                description: card.querySelector('.device-description').value,
            });
        });
        const container = document.getElementById('communication-plan-container');
        container.innerHTML = '<p style="color: var(--text-secondary);">正在重新分析...</p>';
        try {
            const newPlan = await fetchApi('/projects/analyze-communication', 'POST', { device_tasks: deviceTasks });
            renderCommunicationPlan(newPlan);
        } catch (error) {
            container.innerHTML = `<p style="color: var(--accent-error);">分析失败: ${error.message}</p>`;
        }
    }



    // ===================================================================
    // Main Application Flow
    // ===================================================================

    async function handleInitialSubmit() {
        if (userDevices.length === 0) {
            alert('请先注册一个设备才能开始构建项目。');
            return;
        }
        const rawTextInput = document.getElementById('raw-text-input');
        const rawText = rawTextInput.value;
        if (!rawText.trim()) {
            return;
        }

        // --- 新的交互逻辑 ---
        const promptContainer = document.getElementById('initial-prompt-container');
        const inputWrapper = document.getElementById('prompt-input-wrapper');
        const thinkingIndicator = document.getElementById('thinking-indicator');
        const animationContainer = document.getElementById('prompt-animation-container');

        // 1. 创建并播放增强的气泡动画
        const bubble = document.createElement('div');
        bubble.className = 'prompt-bubble';
        bubble.textContent = rawText;
        animationContainer.appendChild(bubble);

        // 创建粒子效果
        createBubbleParticles(animationContainer);

        // 2. 立即隐藏输入框，但延迟显示思考指示器以实现联动
        inputWrapper.style.display = 'none';
        rawTextInput.value = ''; // 清空输入框
        document.getElementById('send-prompt-btn').disabled = true;

        // 3. 延迟显示思考指示器，让气泡先飞一段时间
        setTimeout(() => {
            thinkingIndicator.style.display = 'flex';
            // 添加思考指示器的入场动画
            thinkingIndicator.style.opacity = '0';
            thinkingIndicator.style.transform = 'translateX(-50%) scale(0.8)';
            thinkingIndicator.style.transition = 'all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)';

            // 触发入场动画
            requestAnimationFrame(() => {
                thinkingIndicator.style.opacity = '1';
                thinkingIndicator.style.transform = 'translateX(-50%) scale(1)';

                // 添加脉冲效果，表示"接收"到了气泡
                setTimeout(() => {
                    thinkingIndicator.classList.add('pulse');
                    setTimeout(() => {
                        thinkingIndicator.classList.remove('pulse');
                    }, 600);
                }, 200);
            });
        }, 1200); // 气泡飞行1.2秒后显示思考指示器

        // 4. 动画结束后移除气泡
        setTimeout(() => bubble.remove(), 2000);


        isLocalProject = false;
        localFileHandles.clear();
        lastRenderedStepState = {};
        workflowStreamContainer.innerHTML = '';
        document.getElementById('file-tree-container').innerHTML = '';
        currentEditingPath = null;
        if(monacoEditor) monacoEditor.setValue('');
        document.getElementById('editing-file-name').textContent = '';

        try {
            const analyzeResponse = await fetchApi('/projects/analyze', 'POST', { raw_text: rawText });

            // 成功后，隐藏思考指示器并显示确认对话框
            thinkingIndicator.style.display = 'none';
            populateConfirmationDialog(analyzeResponse);
        } catch (error) {
            alert(`AI分析失败: ${error.message}`);
            // 失败后恢复输入状态
            inputWrapper.style.display = 'flex';
            thinkingIndicator.style.display = 'none';
        }
    }

    // 标准化AI返回的外设数据格式
    function standardizePeripheralData(peripheral) {
        const standardized = { ...peripheral };

        // 处理功能类型映射
        const functionTypeMapping = {
            'distance-sensing': 'DISTANCE_MEASUREMENT',
            'distance_sensing': 'DISTANCE_MEASUREMENT',
            'light-sensing': 'LIGHT_SENSING',
            'light_sensing': 'LIGHT_SENSING',
            'temp-humidity-sensing': 'TEMP_HUMIDITY_SENSING',
            'temp_humidity_sensing': 'TEMP_HUMIDITY_SENSING',
            'motion-detection': 'MOTION_DETECTION',
            'motion_detection': 'MOTION_DETECTION',
            'buzzer': 'BUZZER',
            'led-control': 'LED_CONTROL',
            'led_control': 'LED_CONTROL',
            'relay-switch': 'RELAY_SWITCH',
            'relay_switch': 'RELAY_SWITCH'
        };

        if (functionTypeMapping[standardized.function]) {
            standardized.function = functionTypeMapping[standardized.function];
        }

        // 处理引脚格式转换：从旧格式 pin 转换为新格式 pins
        if (standardized.pin !== undefined && !standardized.pins) {
            if (standardized.pin === "USERINPUT_REQUIRED") {
                standardized.pins = [{ name: "PIN", number: "USERINPUT_REQUIRED" }];
            } else {
                standardized.pins = [{ name: "PIN", number: standardized.pin }];
            }
            delete standardized.pin; // 删除旧格式
        }

        // 确保pins是数组格式
        if (!standardized.pins || !Array.isArray(standardized.pins)) {
            standardized.pins = [{ name: "PIN", number: "USERINPUT_REQUIRED" }];
        }

        // 根据外设型号和功能类型，智能推断引脚名称
        if (standardized.model === "HC-SR04" || standardized.function === "DISTANCE_MEASUREMENT") {
            if (standardized.pins.length === 2) {
                standardized.pins[0].name = "TRIG_PIN";
                standardized.pins[1].name = "ECHO_PIN";
            } else if (standardized.pins.length === 1 && standardized.pins[0].name === "PIN") {
                // 如果只有一个引脚，扩展为两个引脚
                standardized.pins = [
                    { name: "TRIG_PIN", number: standardized.pins[0].number },
                    { name: "ECHO_PIN", number: "USERINPUT_REQUIRED" }
                ];
            }
        } else if (standardized.function === "BUZZER") {
            if (standardized.pins.length === 1 && standardized.pins[0].name === "PIN") {
                standardized.pins[0].name = "CONTROL_PIN";
            }
        }

        return standardized;
    }

    function populateConfirmationDialog(config) {
        stopPolling();
        currentWorkflowId = null;
        isLocalProject = false;

        const confirmationDialog = document.getElementById('confirmation-dialog');

        // 新增：监听关闭事件
        const closeHandler = () => {
            // 恢复主界面输入框
            document.getElementById('prompt-input-wrapper').style.display = 'flex';
            confirmationDialog.removeEventListener('close', closeHandler); // 清理监听器
        };
        confirmationDialog.addEventListener('close', closeHandler);

        const confirmationForm = document.getElementById('confirmation-form');
        confirmationForm.innerHTML = buildConfirmationFormHtml(config);
        confirmationDialog.showModal();

        // 渲染已有的外设
        (config.device_tasks || []).forEach((task, taskIndex) => {
            if (task.peripherals && task.peripherals.length > 0) {
                task.peripherals.forEach(peripheral => {
                    // 标准化AI返回的外设数据
                    const standardizedPeripheral = standardizePeripheralData(peripheral);
                    addPeripheralToTaskForm(standardizedPeripheral, taskIndex);
                });
            }
        });

        document.getElementById('add-device-task-btn').addEventListener('click', () => {
            const container = document.getElementById('device-tasks-container');
            const newIndex = container.children.length;
            container.insertAdjacentHTML('beforeend', createNewDeviceCardHtml(newIndex));
        });


        // --- 【核心修改】为“另存为模板”按钮添加事件监听 ---
        // 防止重复绑定事件监听器
        const saveTemplateBtn = document.getElementById('save-template-btn');
        const newSaveTemplateBtn = saveTemplateBtn.cloneNode(true);
        saveTemplateBtn.parentNode.replaceChild(newSaveTemplateBtn, saveTemplateBtn);

        newSaveTemplateBtn.addEventListener('click', async () => {
            const payload = collectPayloadFromForm();
            const btn = newSaveTemplateBtn;
            btn.disabled = true;
            try {
                await fetchApi('/projects', 'POST', {
                    name: payload.project_name,
                    config_json: payload
                }, 201);
                btn.innerHTML = '<span>已保存!</span>';
                setTimeout(() => {
                    btn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg> 另存为模板';
                    btn.disabled = false;
                }, 2000);
            } catch (error) {
                alert(`保存模板失败: ${error.message}`);
                btn.disabled = false;
            }
        });

        // --- 【核心修改】从 onsubmit 中移除自动保存逻辑 ---
        confirmationForm.onsubmit = async (event) => {
            event.preventDefault();
            confirmationDialog.close();
            const finalPayload = collectPayloadFromForm();
            const workflowView = document.getElementById('view-ai-workflow'); // 获取父容器

            try {
                // 重要修复：启动新工作流前，确保清理之前的状态
                stopPolling();
                currentWorkflowId = null;
                lastRenderedStepState = {};

                document.getElementById('btn-activity-ai').click();
                initialPromptContainer.style.display = 'none';
                workflowStreamContainer.style.display = 'block';

                // [最终修正] 在启动工作流时，为容器添加新样式类以改变布局
                workflowView.classList.add('workflow-active');

                workflowStreamContainer.innerHTML = `<div class="stream-entry"><span class="timestamp">${formatTimestamp()}</span><span class="status-running">[INFO]</span> <span>开始构建新项目: ${finalPayload.project_name}...</span></div>`;

                // 【修复】重置日志计数器，确保新工作流能正确显示智能日志
                window.lastLogCount = 0;

                const startResponse = await fetchApi('/workflows', 'POST', finalPayload, 202);
                startPolling(startResponse.workflow_id);
            } catch (error) {
                // [最终修正] 如果启动失败，恢复布局
                workflowView.classList.remove('workflow-active');
                alert(`操作失败: ${error.message}`);
                // 恢复初始界面
                initialPromptContainer.style.display = 'flex';
                workflowStreamContainer.style.display = 'none';
            }
        };
    }

    // ===================================================================
    // Authentication and User Management (unchanged)
    // ===================================================================
    // ... (login, register, showIde functions are unchanged)
    async function login() {
        try {
            const data = await fetchApi('/auth/login', 'POST', {
                username: document.getElementById('login-username').value,
                password: document.getElementById('login-password').value
            });
            accessToken = data.access_token;
            localStorage.setItem('accessToken', accessToken);
            localStorage.setItem('username', data.user_info.username);
            showIde(data.user_info.username);
        } catch (error) { updateMessage('login-error', error.message, true); }
    }

    async function register() {
        try {
            await fetchApi('/auth/register', 'POST', {
                username: document.getElementById('register-username').value,
                password: document.getElementById('register-password').value
            }, 201);
            updateMessage('register-message', '注册成功！请登录。', false);
            setTimeout(toggleForms, 2000);
        } catch (error) { updateMessage('register-message', error.message, true); }
    }

    function showIde(username) {
        loginView.style.display = 'none';
        ideView.style.display = 'flex';
        document.getElementById('username-display').textContent = username;

        const greetingEl = document.getElementById('welcome-greeting');
        greetingEl.textContent = `Hi! ${username}`;

        // 在所有动画（欢迎语1.5s + 输入框0.5s）结束后，
        // 为父容器添加一个 "animation-finished" 类作为“已完成”的标记。
        setTimeout(() => {
            document.getElementById('view-ai-workflow').classList.add('animation-finished');
        }, 2000); // 1500ms(延迟) + 500ms(动画) = 2000ms

        loadUserDevices();
    }

    // ===================================================================
    // Device Management (unchanged)
    // ===================================================================
    // ... (loadUserDevices, openEditDeviceDialog, handleDeviceFormSubmit, etc. are unchanged)
    async function loadUserDevices() {
        try {
            userDevices = await fetchApi('/devices');
            deviceGrid.innerHTML = '';
            if (userDevices.length === 0) {
                deviceGrid.innerHTML = '<p>您还没有注册任何设备。</p>';
                return;
            }
            userDevices.forEach(dev => {
                const peripheralsText = (dev.peripherals && Array.isArray(dev.peripherals) && dev.peripherals.length > 0)
                    ? dev.peripherals.map(p => p && p.name ? p.name : '未知外设').join(', ')
                    : '无';
                deviceGrid.innerHTML += `
                    <div class="device-card">
                        <div class="device-card-header"><h3>${dev.nickname}</h3><span>${dev.board_model}</span></div>
                        <div class="device-card-body">
                           <p><strong>ID:</strong> ${dev.internal_device_id}</p>
                           <p><strong>外设:</strong> ${peripheralsText}</p>
                        </div>
                        <div class="device-card-actions">
                            <button onclick="openEditDeviceDialog('${dev.internal_device_id}')">编辑</button>
                            <button class="delete-btn" onclick="deleteDevice('${dev.internal_device_id}')">删除</button>
                        </div>

                        <!-- MQTT监控部分 -->
                        <div class="mqtt-section">
                            <div class="mqtt-header">
                                <div class="mqtt-status">
                                    <div class="mqtt-status-indicator disabled" id="mqtt-status-${dev.internal_device_id}"></div>
                                    <span id="mqtt-status-text-${dev.internal_device_id}">未启用</span>
                                </div>
                                <div class="mqtt-controls">
                                    <button onclick="openMqttConfigDialog('${dev.internal_device_id}')" class="secondary">配置MQTT</button>
                                    <button onclick="toggleMqttMonitoring('${dev.internal_device_id}')" id="mqtt-toggle-${dev.internal_device_id}">启用监控</button>
                                    <button onclick="debugMqttStatus('${dev.internal_device_id}')" class="secondary" style="font-size: 0.8em;">🔍 调试</button>
                                </div>
                            </div>
                            <div class="mqtt-log-container" id="mqtt-log-container-${dev.internal_device_id}">
                                <div style="text-align: center; color: var(--text-secondary); padding: 20px;">
                                    暂无MQTT日志
                                </div>
                            </div>
                            <div class="mqtt-log-actions">
                                <span style="font-size: 0.8em; color: var(--text-secondary);">MQTT Explorer</span>
                                <button onclick="clearMqttLogs('${dev.internal_device_id}')" class="secondary">清空日志</button>
                            </div>
                        </div>
                    </div>`;

                // 初始化MQTT状态
                setTimeout(() => initializeMqttStatus(dev.internal_device_id), 100);
            });
        } catch (error) {
            deviceGrid.innerHTML = `<p style="color: var(--accent-error);">加载设备失败: ${error.message}</p>`;
        }
    }

    function openEditDeviceDialog(deviceId) {
        const device = userDevices.find(d => d.internal_device_id === deviceId);
        if (!device) return;
        document.getElementById('device-dialog').showModal();
        document.getElementById('device-dialog-title').textContent = '编辑设备';
        document.getElementById('device-form-submit-btn').textContent = '更新';
        document.getElementById('device-internal-id').value = device.internal_device_id || '';
        document.getElementById('device-nickname').value = device.nickname || '';
        document.getElementById('device-board').value = device.board_model || '';
        document.getElementById('cloud-platform').value = device.cloud_platform || 'tuya';
        document.getElementById('cloud-product-id').value = device.cloud_product_id || '';
        document.getElementById('cloud-device-id').value = device.cloud_device_id || '';
        document.getElementById('cloud-device-secret').value = device.cloud_device_secret || '';
        renderDevicePeripherals(device.peripherals);
    }

    async function handleDeviceFormSubmit(event) {
        event.preventDefault();
        const internalId = document.getElementById('device-internal-id').value;
        const isEditing = !!internalId;
        const payload = {
            nickname: document.getElementById('device-nickname').value,
            board_model: document.getElementById('device-board').value,
            cloud_platform: document.getElementById('cloud-platform').value,
            cloud_product_id: document.getElementById('cloud-product-id').value,
            cloud_device_id: document.getElementById('cloud-device-id').value,
            cloud_device_secret: document.getElementById('cloud-device-secret').value
        };
        const peripherals = [];
        document.querySelectorAll('#device-peripherals-container .peripheral-item').forEach(item => {
            // 从 data-config 属性读取外设配置
            const configString = item.dataset.config;
            if (configString) {
                try {
                    const peripheralConfig = JSON.parse(configString);
                    // 从配置中提取外设信息
                    peripherals.push({
                        name: peripheralConfig.name,
                        model: peripheralConfig.model,
                        interface: peripheralConfig.interface,
                        function: peripheralConfig.function,
                        implementationType: peripheralConfig.implementationType,
                        pins: peripheralConfig.pins || [],
                        config: peripheralConfig.config || {},
                        description: peripheralConfig.description
                    });
                } catch (e) {
                    console.error('解析外设配置失败:', e, configString);
                }
            }
        });
        payload.peripherals = peripherals;
        const url = isEditing ? `/devices/${internalId}` : '/devices';
        const method = isEditing ? 'PUT' : 'POST';
        try {
            await fetchApi(url, method, payload, isEditing ? 200 : 201);
            document.getElementById('device-dialog').close();
            await loadUserDevices();
        } catch (error) {
            updateMessage('device-form-error', error.message, true);
        }
    }

    function renderDevicePeripherals(peripherals = []) {
        const container = document.getElementById('device-peripherals-container');
        container.innerHTML = '<h4>外设配置</h4>';
        container.innerHTML += `
             <div style="display: grid; grid-template-columns: 1.5fr 1.5fr 2.5fr auto; gap: 10px; color: var(--text-secondary); font-size: 0.9em; margin-bottom: 5px; padding: 0 5px;">
                <span>名称</span><span>型号</span><span>引脚配置</span><span></span>
            </div>
        `;
        if (!peripherals || peripherals.length === 0) {
            container.innerHTML += '<p class="text-secondary" style="font-size: 0.9em; text-align: center; margin: 10px 0;">该设备暂未配置外设</p>';
        } else {
            peripherals.forEach((p, index) => {
                // 处理旧格式数据兼容性：如果存在pin字段，转换为pins数组
                let pins = p.pins || [];
                if (p.pin !== undefined && p.pin !== null && p.pin !== '' && pins.length === 0) {
                    pins = [{ name: 'pin', number: p.pin }];
                }

                // 构建外设配置对象，兼容旧版AI分析结果
                const peripheralConfig = {
                    function: (p.function && p.function !== 'undefined' && p.function !== 'undefned') ? p.function : inferFunctionFromModel(p.model || p.name || ''),
                    implementationType: (p.implementationType && p.implementationType !== 'undefined') ? p.implementationType : 'GENERIC_COMPONENT',
                    name: p.name || '',
                    model: p.model || '',
                    interface: p.interface || inferInterfaceFromModel(p.model || p.name || ''),
                    pins: pins,
                    config: p.config || {},
                    description: p.description || ''
                };

                const configAttr = `data-config='${escapeHtml(JSON.stringify(peripheralConfig))}'`;

                const peripheralHtml = `
                    <div class="peripheral-item" ${configAttr} style="display: grid; grid-template-columns: 1.5fr 1.5fr 2.5fr auto; gap: 10px; align-items: start; margin-bottom: 10px; background: var(--card-background); border: 1px solid var(--border-color); border-radius: 6px; padding: 10px;">
                        <div style="font-weight: bold; color: var(--text-primary);">${escapeHtml(p.name || '')}</div>
                        <div style="color: var(--text-secondary);">${escapeHtml(p.model || '')}</div>
                        <div class="pins-container" style="width: 100%; overflow: hidden;">
                            ${pins.map(pin => `
                                <div class="pin-item" style="display: flex; gap: 8px; margin-bottom: 5px; align-items: center; width: 100%;">
                                    <span style="width: 45%; min-width: 50px; font-size: 0.85em; color: var(--text-secondary);">${escapeHtml(pin.name || '')}:</span>
                                    <span style="width: 35%; min-width: 40px; font-size: 0.85em; font-weight: bold;">GPIO ${pin.number || ''}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div style="display: flex; gap: 5px;">
                            <button type="button" class="edit-peripheral-btn" onclick="editPeripheral(this)" title="编辑外设" style="background: var(--primary-color); color: white; border: none; border-radius: 4px; padding: 5px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.8'" onmouseout="this.style.opacity='1'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                            </button>
                            <button type="button" class="remove-peripheral-btn" onclick="this.closest('.peripheral-item').remove()" title="删除外设" style="background: var(--accent-error); color: white; border: none; border-radius: 4px; padding: 5px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--accent-error-hover)'" onmouseout="this.style.background='var(--accent-error)'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                            </button>
                        </div>
                    </div>`;
                container.insertAdjacentHTML('beforeend', peripheralHtml);
            });
        }
    }

    // ===================================================================
    // Smart Peripheral Configuration Data
    // ===================================================================

    const peripheralDatabase = {
        LIGHT_SENSING: {
            name: "光照检测",
            specificModels: [
                {
                    name: "BH1750",
                    description: "数字光照传感器，I2C接口",
                    interface: "I2C",
                    pins: [
                        { name: "SDA", type: "gpio", required: true, description: "I2C数据线" },
                        { name: "SCL", type: "gpio", required: true, description: "I2C时钟线" }
                    ],
                    config: [
                        { name: "i2c_address", type: "text", default: "0x23", description: "I2C地址" }
                    ]
                },
                {
                    name: "TEMT6000",
                    description: "模拟光照传感器",
                    interface: "ANALOG",
                    pins: [
                        { name: "ANALOG_PIN", type: "gpio", required: true, description: "模拟输入引脚" }
                    ],
                    config: []
                }
            ],
            genericComponents: [
                {
                    name: "模拟光敏电阻",
                    description: "通用光敏电阻，需要分压电路",
                    interface: "ANALOG",
                    pins: [
                        { name: "ANALOG_PIN", type: "gpio", required: true, description: "模拟输入引脚" }
                    ],
                    config: []
                },
                {
                    name: "数字光电开关",
                    description: "数字输出的光电开关",
                    interface: "DIGITAL",
                    pins: [
                        { name: "DIGITAL_PIN", type: "gpio", required: true, description: "数字输入引脚" }
                    ],
                    config: []
                }
            ]
        },
        TEMP_HUMIDITY_SENSING: {
            name: "温湿度检测",
            specificModels: [
                {
                    name: "DHT22",
                    description: "数字温湿度传感器，单总线接口",
                    interface: "ONE_WIRE",
                    pins: [
                        { name: "DATA_PIN", type: "gpio", required: true, description: "数据引脚" },
                        { name: "POWER_PIN", type: "gpio", required: false, description: "电源控制引脚（可选）" }
                    ],
                    config: []
                },
                {
                    name: "DHT11",
                    description: "数字温湿度传感器，单总线接口",
                    interface: "ONE_WIRE",
                    pins: [
                        { name: "DATA_PIN", type: "gpio", required: true, description: "数据引脚" }
                    ],
                    config: []
                }
            ],
            genericComponents: [
                {
                    name: "模拟温度传感器",
                    description: "如LM35等模拟温度传感器",
                    interface: "ANALOG",
                    pins: [
                        { name: "ANALOG_PIN", type: "gpio", required: true, description: "模拟输入引脚" }
                    ],
                    config: []
                }
            ]
        },
        MOTION_DETECTION: {
            name: "运动检测",
            specificModels: [
                {
                    name: "HC-SR501",
                    description: "人体红外传感器",
                    interface: "DIGITAL",
                    pins: [
                        { name: "DIGITAL_PIN", type: "gpio", required: true, description: "数字输出引脚" }
                    ],
                    config: []
                }
            ],
            genericComponents: [
                {
                    name: "通用PIR传感器",
                    description: "通用人体红外传感器",
                    interface: "DIGITAL",
                    pins: [
                        { name: "DIGITAL_PIN", type: "gpio", required: true, description: "数字输出引脚" }
                    ],
                    config: []
                }
            ]
        },
        DISTANCE_MEASUREMENT: {
            name: "距离测量",
            specificModels: [
                {
                    name: "HC-SR04",
                    description: "超声波距离传感器",
                    interface: "DIGITAL",
                    pins: [
                        { name: "TRIG_PIN", type: "gpio", required: true, description: "触发引脚" },
                        { name: "ECHO_PIN", type: "gpio", required: true, description: "回响引脚" }
                    ],
                    config: []
                }
            ],
            genericComponents: [
                {
                    name: "通用超声波传感器",
                    description: "通用超声波距离传感器",
                    interface: "DIGITAL",
                    pins: [
                        { name: "TRIG_PIN", type: "gpio", required: true, description: "触发引脚" },
                        { name: "ECHO_PIN", type: "gpio", required: true, description: "回响引脚" }
                    ],
                    config: []
                }
            ]
        },
        ATTITUDE_DETECTION: {
            name: "姿态检测",
            specificModels: [
                {
                    name: "MPU6050",
                    description: "6轴陀螺仪加速度计，I2C接口",
                    interface: "I2C",
                    pins: [
                        { name: "SDA", type: "gpio", required: true, description: "I2C数据线" },
                        { name: "SCL", type: "gpio", required: true, description: "I2C时钟线" }
                    ],
                    config: [
                        { name: "i2c_address", type: "text", default: "0x68", description: "I2C地址" }
                    ]
                },
                {
                    name: "ADXL345",
                    description: "3轴加速度计，I2C接口",
                    interface: "I2C",
                    pins: [
                        { name: "SDA", type: "gpio", required: true, description: "I2C数据线" },
                        { name: "SCL", type: "gpio", required: true, description: "I2C时钟线" }
                    ],
                    config: [
                        { name: "i2c_address", type: "text", default: "0x53", description: "I2C地址" }
                    ]
                }
            ],
            genericComponents: []
        },
        RELAY_SWITCH: {
            name: "继电器开关",
            specificModels: [],
            genericComponents: [
                {
                    name: "通用继电器模块",
                    description: "数字控制的继电器开关",
                    interface: "DIGITAL",
                    pins: [
                        { name: "CONTROL_PIN", type: "gpio", required: true, description: "控制引脚" }
                    ],
                    config: []
                }
            ]
        },
        BUZZER: {
            name: "蜂鸣器",
            specificModels: [],
            genericComponents: [
                {
                    name: "有源蜂鸣器",
                    description: "数字控制的有源蜂鸣器",
                    interface: "DIGITAL",
                    pins: [
                        { name: "CONTROL_PIN", type: "gpio", required: true, description: "控制引脚" }
                    ],
                    config: []
                },
                {
                    name: "无源蜂鸣器",
                    description: "PWM控制的无源蜂鸣器",
                    interface: "PWM",
                    pins: [
                        { name: "PWM_PIN", type: "gpio", required: true, description: "PWM输出引脚" }
                    ],
                    config: []
                },
                {
                    name: "USERINPUT_REQUIRED",
                    description: "需要用户手动配置的蜂鸣器",
                    interface: "DIGITAL",
                    pins: [
                        { name: "CONTROL_PIN", type: "gpio", required: true, description: "控制引脚" }
                    ],
                    config: []
                }
            ]
        },
        LED_CONTROL: {
            name: "LED控制",
            specificModels: [],
            genericComponents: [
                {
                    name: "普通LED",
                    description: "数字控制的LED",
                    interface: "DIGITAL",
                    pins: [
                        { name: "LED_PIN", type: "gpio", required: true, description: "LED控制引脚" }
                    ],
                    config: []
                },
                {
                    name: "PWM调光LED",
                    description: "PWM调光控制的LED",
                    interface: "PWM",
                    pins: [
                        { name: "PWM_PIN", type: "gpio", required: true, description: "PWM输出引脚" }
                    ],
                    config: []
                }
            ]
        },
        PRESSURE_SENSING: {
            name: "压力检测",
            specificModels: [
                {
                    name: "BMP280",
                    description: "数字气压传感器，I2C接口",
                    interface: "I2C",
                    pins: [
                        { name: "SDA", type: "gpio", required: true, description: "I2C数据线" },
                        { name: "SCL", type: "gpio", required: true, description: "I2C时钟线" }
                    ],
                    config: [
                        { name: "i2c_address", type: "text", default: "0x76", description: "I2C地址" }
                    ]
                },
                {
                    name: "MPX5700AP",
                    description: "模拟压力传感器",
                    interface: "ANALOG",
                    pins: [
                        { name: "ANALOG_PIN", type: "gpio", required: true, description: "模拟输入引脚" }
                    ],
                    config: []
                }
            ],
            genericComponents: [
                {
                    name: "模拟压力传感器",
                    description: "通用模拟压力传感器",
                    interface: "ANALOG",
                    pins: [
                        { name: "ANALOG_PIN", type: "gpio", required: true, description: "模拟输入引脚" }
                    ],
                    config: []
                }
            ]
        },
        TEMPERATURE_SENSING: {
            name: "温度检测",
            specificModels: [
                {
                    name: "DS18B20",
                    description: "数字温度传感器，单总线接口",
                    interface: "ONE_WIRE",
                    pins: [
                        { name: "DATA_PIN", type: "gpio", required: true, description: "数据引脚" }
                    ],
                    config: []
                },
                {
                    name: "LM35",
                    description: "模拟温度传感器",
                    interface: "ANALOG",
                    pins: [
                        { name: "ANALOG_PIN", type: "gpio", required: true, description: "模拟输入引脚" }
                    ],
                    config: []
                }
            ],
            genericComponents: [
                {
                    name: "热敏电阻",
                    description: "通用热敏电阻温度传感器",
                    interface: "ANALOG",
                    pins: [
                        { name: "ANALOG_PIN", type: "gpio", required: true, description: "模拟输入引脚" }
                    ],
                    config: []
                }
            ]
        },
        DISPLAY: {
            name: "显示屏",
            specificModels: [
                {
                    name: "SSD1306",
                    description: "OLED显示屏，I2C接口",
                    interface: "I2C",
                    pins: [
                        { name: "SDA", type: "gpio", required: true, description: "I2C数据线" },
                        { name: "SCL", type: "gpio", required: true, description: "I2C时钟线" }
                    ],
                    config: [
                        { name: "i2c_address", type: "text", default: "0x3C", description: "I2C地址" },
                        { name: "screen_width", type: "number", default: "128", description: "屏幕宽度" },
                        { name: "screen_height", type: "number", default: "64", description: "屏幕高度" }
                    ]
                },
                {
                    name: "LCD1602",
                    description: "字符液晶显示屏，I2C接口",
                    interface: "I2C",
                    pins: [
                        { name: "SDA", type: "gpio", required: true, description: "I2C数据线" },
                        { name: "SCL", type: "gpio", required: true, description: "I2C时钟线" }
                    ],
                    config: [
                        { name: "i2c_address", type: "text", default: "0x27", description: "I2C地址" }
                    ]
                }
            ],
            genericComponents: []
        },
        UNKNOWN: {
            name: "未知外设",
            specificModels: [],
            genericComponents: [
                {
                    name: "通用外设",
                    description: "未识别的外设类型，请重新配置",
                    interface: "UNKNOWN",
                    pins: [
                        { name: "PIN", type: "gpio", required: false, description: "通用引脚" }
                    ],
                    config: []
                }
            ]
        }
    };

        // 辅助函数：从模型名称推断功能类型（兼容旧版AI分析结果）
        function inferFunctionFromModel(modelName) {
            const name = modelName.toLowerCase();
            if (name.includes('bh1750') || name.includes('光照') || name.includes('light')) {
                return 'LIGHT_SENSING';
            } else if (name.includes('dht') || name.includes('温湿度') || name.includes('humidity')) {
                return 'TEMP_HUMIDITY_SENSING';
            } else if (name.includes('ds18') || name.includes('lm35') || name.includes('温度') || name.includes('temperature')) {
                return 'TEMPERATURE_SENSING';
            } else if (name.includes('pir') || name.includes('hc-sr501') || name.includes('运动') || name.includes('motion')) {
                return 'MOTION_DETECTION';
            } else if (name.includes('hc-sr04') || name.includes('距离') || name.includes('distance')) {
                return 'DISTANCE_MEASUREMENT';
            } else if (name.includes('mpu') || name.includes('adxl') || name.includes('姿态') || name.includes('attitude')) {
                return 'ATTITUDE_DETECTION';
            } else if (name.includes('继电器') || name.includes('relay')) {
                return 'RELAY_SWITCH';
            } else if (name.includes('蜂鸣器') || name.includes('buzzer')) {
                return 'BUZZER';
            } else if (name.includes('led') || name.includes('灯')) {
                return 'LED_CONTROL';
            } else if (name.includes('bmp') || name.includes('压力') || name.includes('pressure')) {
                return 'PRESSURE_SENSING';
            } else if (name.includes('ssd1306') || name.includes('lcd') || name.includes('显示') || name.includes('display')) {
                return 'DISPLAY';
            }
            return 'UNKNOWN';
        }

        // 辅助函数：从模型名称推断接口类型（兼容旧版AI分析结果）
        function inferInterfaceFromModel(modelName) {
            const name = modelName.toLowerCase();
            if (name.includes('bh1750') || name.includes('ssd1306') || name.includes('lcd') ||
                name.includes('mpu') || name.includes('adxl') || name.includes('bmp')) {
                return 'I2C';
            } else if (name.includes('dht') || name.includes('ds18')) {
                return 'ONE_WIRE';
            } else if (name.includes('lm35') || name.includes('光敏') || name.includes('热敏')) {
                return 'ANALOG';
            } else if (name.includes('hc-sr501') || name.includes('继电器') || name.includes('蜂鸣器')) {
                return 'DIGITAL';
            } else if (name.includes('pwm') || name.includes('调光')) {
                return 'PWM';
            }
            return 'DIGITAL'; // 默认为数字接口
        }

        function generatePinInputs() {
            const container = document.getElementById('pin-inputs-container');
            const pinConfigContainer = document.getElementById('pin-config-container');

            if (!selectedComponent || !selectedComponent.pins || selectedComponent.pins.length === 0) {
                pinConfigContainer.style.display = 'none';
                container.innerHTML = '';
                return;
            }

            pinConfigContainer.style.display = 'block';

            let html = '';
            selectedComponent.pins.forEach((pin, index) => {
                const required = pin.required ? 'required' : '';
                const placeholder = pin.required ? '必填' : '可选';

                html += `
                    <div class="form-group" style="margin-bottom: 10px;">
                        <label for="pin-${index}">${pin.name} ${pin.required ? '*' : ''}</label>
                        <input type="number" id="pin-${index}" placeholder="${placeholder} - ${pin.description}" ${required} min="0" max="39">
                        <small style="color: var(--text-secondary); font-size: 0.8em;">${pin.description}</small>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function generateAdditionalConfig() {
            const container = document.getElementById('additional-inputs-container');
            const additionalConfigContainer = document.getElementById('additional-config-container');

            if (!selectedComponent || !selectedComponent.config || selectedComponent.config.length === 0) {
                additionalConfigContainer.style.display = 'none';
                container.innerHTML = '';
                return;
            }

            additionalConfigContainer.style.display = 'block';

            let html = '';
            selectedComponent.config.forEach((config, index) => {
                html += `
                    <div class="form-group" style="margin-bottom: 10px;">
                        <label for="config-${index}">${config.description}</label>
                        <input type="${config.type}" id="config-${index}" value="${config.default || ''}" placeholder="${config.description}">
                    </div>
                `;
            });

            container.innerHTML = html;
        }



    async function deleteDevice(deviceId) {
        if (!confirm('确定要删除这个设备吗？')) return;
        try {
            await fetchApi(`/devices/${deviceId}`, 'DELETE');
            await loadUserDevices();
        } catch (error) { alert(`删除失败: ${error.message}`); }
    }

    // ===================================================================
    // File System (Local & Remote) (unchanged)
    // ===================================================================
    // ... (openLocalFolder, processDirectory, buildTreeHtml, etc. are unchanged)
    async function openLocalFolder() {
        if (!window.showDirectoryPicker) {
            alert('您的浏览器不支持此功能。请使用最新版本的Chrome或Edge浏览器。');
            return;
        }
        try {
            const dirHandle = await window.showDirectoryPicker();
            stopPolling();
            isLocalProject = true;
            currentWorkflowId = null; // 重置云端项目ID
            syncedLocalProjectId = null; // 重置已同步的项目ID
            localFileHandles.clear();



            const treeContainer = document.getElementById('file-tree-container');
            treeContainer.innerHTML = '<p style="padding: 10px; color: var(--text-secondary);">正在读取文件夹...</p>';

            const treeData = await processDirectory(dirHandle);

            if (treeData.length > 0) {
                treeContainer.innerHTML = '';
                treeContainer.appendChild(buildTreeHtml(treeData, ''));
            } else {
                treeContainer.innerHTML = '<p style="padding: 10px; color: var(--text-secondary);">文件夹为空。</p>';
            }

            document.querySelector('#status-workflow span').textContent = dirHandle.name;
            document.querySelector('#status-device span').textContent = '本地项目';

            // 强制显示烧录按钮
            const flashBtn = document.getElementById('flash-firmware-btn');
            if (flashBtn) {
                flashBtn.style.display = 'inline-flex';
            }

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('打开文件夹失败:', error);
                alert(`打开文件夹失败: ${error.message}`);
            }
        }
    }

    function showFlashResult(success, message, keyErrors = '', fullOutput = '', suggestions = []) {
        // 创建结果显示模态框
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: #1a1a28;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            max-width: 80%;
            max-height: 80%;
            overflow: auto;
            color: #ffffff;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        `;

        const title = success ? '🎉 烧录成功' : '❌ 烧录失败';
        const titleColor = success ? '#4CAF50' : '#f44336';

        content.innerHTML = `
            <h3 style="color: ${titleColor}; margin-top: 0;">${title}</h3>
            <p><strong>消息:</strong> ${message}</p>
            ${(suggestions && Array.isArray(suggestions) && suggestions.length > 0) ? `
                <div style="margin-top: 15px; padding: 15px; background: #2d4a22; border: 1px solid #4caf50; border-radius: 6px;">
                    <strong style="color: #81c784;">💡 解决建议:</strong>
                    <ul style="margin: 8px 0; padding-left: 20px; color: #e8f5e8;">
                        ${suggestions.map(s => `<li style="margin: 4px 0;">${s || '未知建议'}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
            ${keyErrors ? `
                <div style="margin-top: 15px;">
                    <strong style="color: #ff8a80;">🔍 关键错误信息:</strong>
                    <pre style="
                        background: #2d1b1b;
                        border: 1px solid #f44336;
                        padding: 15px;
                        border-radius: 6px;
                        overflow: auto;
                        max-height: 250px;
                        font-size: 14px;
                        line-height: 1.4;
                        white-space: pre-wrap;
                        margin-top: 8px;
                        color: #ffcdd2;
                        font-family: 'Consolas', 'Monaco', monospace;
                    ">${keyErrors}</pre>
                </div>
            ` : ''}
            ${fullOutput ? `
                <details style="margin-top: 15px;">
                    <summary style="cursor: pointer; font-weight: bold; color: #90caf9;">完整输出日志 (调试用)</summary>
                    <pre style="
                        background: #0d1117;
                        border: 1px solid #30363d;
                        padding: 15px;
                        border-radius: 6px;
                        overflow: auto;
                        max-height: 400px;
                        font-size: 13px;
                        line-height: 1.3;
                        white-space: pre-wrap;
                        margin-top: 10px;
                        color: #c9d1d9;
                        font-family: 'Consolas', 'Monaco', monospace;
                    ">${fullOutput}</pre>
                </details>
            ` : ''}
            <div style="text-align: right; margin-top: 20px;">
                <button onclick="this.closest('.flash-result-modal').remove()"
                        style="
                            background: var(--accent-color);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                        ">关闭</button>
            </div>
        `;

        modal.className = 'flash-result-modal';
        modal.appendChild(content);
        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    async function syncLocalProject() {
        // 检查是否有文件树内容，如果有但 localFileHandles 为空，说明需要重新获取文件句柄
        const treeContainer = document.getElementById('file-tree-container');
        const hasFileTree = treeContainer.children.length > 0;

        if (!isLocalProject && !hasFileTree) {
            throw new Error("没有可同步的本地项目。");
        }

        if (localFileHandles.size === 0 && hasFileTree) {
            throw new Error("本地文件句柄丢失，请重新打开本地文件夹。");
        }

        const syncButton = document.getElementById('sync-local-project-btn');
        if (syncButton) {
            syncButton.disabled = true;
            syncButton.textContent = '同步中...';
        }

        try {
            const zip = new JSZip();
            // 递归地将文件句柄添加到zip中
            for (const [path, handle] of localFileHandles.entries()) {
                if (handle.kind === 'file') {
                    const file = await handle.getFile();
                    zip.file(path, file);
                }
            }

            const zipBlob = await zip.generateAsync({ type: "blob" });

            const formData = new FormData();
            formData.append('project_zip', zipBlob, 'project.zip');

            // 使用原生的 fetch 发送 FormData
            const response = await fetch(`${API_BASE_URL}/projects/sync-local`, {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${accessToken}` },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `同步失败，状态码: ${response.status}`);
            }

            const result = await response.json();
            syncedLocalProjectId = result.localProjectId; // 保存返回的ID

            if (syncButton) {
                syncButton.textContent = '同步成功!';
                syncButton.onclick = null; // 禁用按钮的再次点击
            }

        } catch (error) {
            if (syncButton) {
                syncButton.textContent = '同步失败';
                setTimeout(() => {
                    syncButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>`;
                    syncButton.disabled = false;
                }, 3000);
            }
            throw error; // 重新抛出错误，让调用者处理
        }
    }

    async function processDirectory(dirHandle, path = '') {
        const entries = [];
        localFileHandles.set(path || dirHandle.name, dirHandle);
        for await (const entry of dirHandle.values()) {
            const currentPath = path ? `${path}/${entry.name}` : entry.name;
            localFileHandles.set(currentPath, entry);
            if (entry.kind === 'directory') {
                entries.push({
                    name: entry.name,
                    type: 'folder',
                    children: await processDirectory(entry, currentPath)
                });
            } else {
                entries.push({ name: entry.name, type: 'file' });
            }
        }
        entries.sort((a, b) => {
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;
            return a.name.localeCompare(b.name);
        });
        return entries;
    }

    function buildTreeHtml(nodes, path = '', pathToReveal = null) {
        const ul = document.createElement('ul');
        nodes.forEach(node => {
            const li = document.createElement('li');
            const currentPath = path ? `${path}/${node.name}` : node.name;
            li.dataset.path = currentPath;
            if (node.type === 'folder') {
                li.className = 'folder';
                if (pathToReveal && pathToReveal.startsWith(currentPath + '/')) {
                    li.classList.add('open');
                }
                li.innerHTML = `<div class="tree-item"><span class="icon"></span>${node.name}</div>`;
                if (node.children && node.children.length > 0) {
                    const nestedUl = buildTreeHtml(node.children, currentPath, pathToReveal);
                    nestedUl.className = 'nested';
                    li.appendChild(nestedUl);
                }
                li.querySelector('.tree-item').onclick = (e) => { e.stopPropagation(); li.classList.toggle('open'); };
            } else {
                li.className = 'file';
                li.innerHTML = `<div class="tree-item"><span class="icon"></span>${node.name}</div>`;
                if (pathToReveal && pathToReveal === currentPath) {
                     setTimeout(() => {
                        document.querySelectorAll('.tree-item.active').forEach(el => el.classList.remove('active'));
                        li.querySelector('.tree-item').classList.add('active');
                     }, 0);
                }
                li.querySelector('.tree-item').onclick = (e) => {
                    e.stopPropagation();
                    document.querySelectorAll('.tree-item.active').forEach(el => el.classList.remove('active'));
                    e.currentTarget.classList.add('active');
                    viewFileContent(currentPath);
                };
            }
            ul.appendChild(li);
        });
        return ul;
    }

    async function loadAndRenderFileTree(workflowId, pathToReveal = null) {
        if (!workflowId) {
            console.warn("loadAndRenderFileTree called without a workflowId.");
            return;
        }
        currentWorkflowId = workflowId;
        isLocalProject = false;



        const treeContainer = document.getElementById('file-tree-container');
        treeContainer.innerHTML = '<p style="padding: 10px; color: var(--text-secondary);">加载文件中...</p>';
        try {
            const treeData = await fetchApi(`/workflows/${currentWorkflowId}/files`);
            if (treeData?.length > 0) {
                treeContainer.innerHTML = '';
                treeContainer.appendChild(buildTreeHtml(treeData, '', pathToReveal));
            } else {
                treeContainer.innerHTML = '<p style="padding: 10px; color: var(--text-secondary);">工作区为空。</p>';
            }
        } catch (error) {
            treeContainer.innerHTML = `<p style="padding: 10px; color: var(--accent-error);">加载文件树失败: ${error.message}</p>`;
        }
    }

    async function viewFileContent(filePath) {
        if (!monacoEditor) { return; }

        // 新增：打开新文件时，立即清空上一个文件的错误标记
        monaco.editor.setModelMarkers(monacoEditor.getModel(), 'syntax-checker', []);
        renderProblemsPanel([]); // 同时隐藏并清空问题面板

        // 显示编辑器，隐藏欢迎页
        document.getElementById('editor-welcome-message').style.display = 'none';
        document.getElementById('code-editor-panel').style.display = 'flex';

        monacoEditor.updateOptions({ readOnly: true });
        monacoEditor.setValue('// 正在加载文件...\n// ' + filePath);
        document.getElementById('editing-file-name').textContent = filePath;
        currentEditingPath = filePath;

        try {
            let content = '';
            if (isLocalProject) {
                const handle = localFileHandles.get(filePath);
                if (handle && handle.kind === 'file') {
                    const file = await handle.getFile();
                    content = await file.text();
                } else { throw new Error('找不到文件句柄'); }
            } else {
                const data = await fetchApi(`/workflows/${currentWorkflowId}/files?path=${encodeURIComponent(filePath)}`);
                content = data.content;
            }

            const extension = filePath.split('.').pop();
            const langMap = {
                'js': 'javascript', 'html': 'html', 'css': 'css',
                'py': 'python', 'json': 'json', 'md': 'markdown',
                'cpp': 'cpp', 'h': 'cpp', 'ino': 'cpp',
                'c': 'c', 'hpp': 'cpp'
            };
            const language = langMap[extension] || 'plaintext';

            monacoEditor.setValue(content);
            monaco.editor.setModelLanguage(monacoEditor.getModel(), language);
            monacoEditor.updateOptions({ readOnly: false });

        } catch (error) {
            monacoEditor.setValue(`// 加载文件失败: ${error.message}`);
        }
    }

    function renderProblemsPanel(markers) {
        const panel          = document.getElementById('problems-panel');
        const content        = document.getElementById('problems-panel-content');
        const problemsToggle = document.getElementById('status-problems');

        /* 0. 无问题时隐藏 */
        if (!markers || markers.length === 0) {
            toggleProblemsPanel(false);
            problemsToggle.style.display = 'none';
            return;
        }

        /* 1. 统计并自动选择过滤级别 */
        const errorCount   = markers.filter(m => m.severity === monaco.MarkerSeverity.Error  ).length;
        const warningCount = markers.filter(m => m.severity === monaco.MarkerSeverity.Warning).length;
        const infoCount    = markers.filter(m => m.severity === monaco.MarkerSeverity.Info   ).length;
        if (problemsFilter === 'all') {
            if      (errorCount)   problemsFilter = 'errors';
            else if (warningCount) problemsFilter = 'warnings';
            else if (infoCount)    problemsFilter = 'info';
        }

        /* 2. 右下角图标 */
        const activeClass = t => (problemsFilter === t ? 'active' : '');
        let statusHtml = '';

        if (errorCount) {
            statusHtml += `
              <div class="status-item ${activeClass('errors')}"
                   onclick="filterProblems('errors')" style="cursor:pointer;">
                <svg class="icon-error" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                     viewBox="0 0 16 16" fill="none" stroke="currentColor" stroke-width="1.5"
                     stroke-linecap="round" stroke-linejoin="round">
                  <path d="M8,15 C4.134,15 1,11.866 1,8
                           C1,4.134 4.134,1 8,1
                           C11.866,1 15,4.134 15,8
                           C15,11.866 11.866,15 8,15 Z
                           M8,9.5 L8,5 M8,12.5 L8,12.501"/>
                </svg><span>${errorCount}</span>
              </div>`;
        }
        if (warningCount) {
            statusHtml += `
              <div class="status-item ${activeClass('warnings')}"
                   onclick="filterProblems('warnings')" style="cursor:pointer;">
                <svg class="icon-warning" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                     viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                     stroke-linecap="round" stroke-linejoin="round">
                  <path d="m21.73 18-8-14a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16 a2 2 0 0 0 1.73-3Z"/>
                  <line x1="12" y1="9"  x2="12"    y2="13"/>
                  <line x1="12" y1="17" x2="12.01" y2="17"/>
                </svg><span>${warningCount}</span>
              </div>`;
        }
        if (infoCount) {
            statusHtml += `
              <div class="status-item ${activeClass('info')}"
                   onclick="filterProblems('info')" style="cursor:pointer;">
                <svg class="icon-info" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                     viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                     stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"/>
                  <line x1="12" y1="16" x2="12"   y2="12"/>
                  <line x1="12" y1="8"  x2="12.01"y2="8"/>
                </svg><span>${infoCount}</span>
              </div>`;
        }
        problemsToggle.innerHTML     = statusHtml;
        problemsToggle.style.display = 'flex';  // 反正有 marker 就展示

        /* 3. 面板详细列表 */
        const sevColor = {
            [monaco.MarkerSeverity.Error]  : 'var(--accent-error)',
            [monaco.MarkerSeverity.Warning]: 'var(--accent-warn)',
            [monaco.MarkerSeverity.Info]   : 'var(--accent-primary)'
        };
        const sevText = {
            [monaco.MarkerSeverity.Error]  : 'Error',
            [monaco.MarkerSeverity.Warning]: 'Warning',
            [monaco.MarkerSeverity.Info]   : 'Info'
        };

        const makeSection = (sev, title) => {
            const list = markers.filter(m => m.severity === sev);
            if (!list.length) return '';
            let html = `<h4 style="margin:10px 0 5px;font-weight:500;font-family:var(--font-family);
                                  border-bottom:1px solid var(--border-color);">
                          ${title} <span style="font-size:.85em;color:var(--text-secondary)">(${list.length})</span>
                        </h4><ul style="list-style:none;padding-left:5px;">`;
            list.forEach(m=>{
                const jump = `monacoEditor.revealLineInCenter(${m.startLineNumber});
                              monacoEditor.setPosition({lineNumber:${m.startLineNumber},column:${m.startColumn}});
                              monacoEditor.focus();`;
                html += `<li style="padding:4px;border-radius:4px;cursor:pointer;"
                             onmouseover="this.style.backgroundColor='var(--bg-surface-hover)'"
                             onmouseout ="this.style.backgroundColor='transparent'"
                             onclick="${jump}">
                           <span style="display:inline-block;width:65px;font-weight:500;color:${sevColor[sev]};">
                             ${sevText[sev]}
                           </span>
                           <span style="color:var(--text-secondary);">[L${m.startLineNumber},C${m.startColumn}]</span>:
                           <span>${escapeHtml(m.message)}</span>
                         </li>`;
            });
            return html + '</ul>';
        };

        let contentHtml = '';
        if (problemsFilter === 'all') {
            contentHtml += makeSection(monaco.MarkerSeverity.Error  ,'错误');
            contentHtml += makeSection(monaco.MarkerSeverity.Warning,'警告');
            contentHtml += makeSection(monaco.MarkerSeverity.Info   ,'信息');
        } else if (problemsFilter === 'errors')   contentHtml = makeSection(monaco.MarkerSeverity.Error  ,'错误');
        else if (problemsFilter === 'warnings')   contentHtml = makeSection(monaco.MarkerSeverity.Warning,'警告');
        else if (problemsFilter === 'info')       contentHtml = makeSection(monaco.MarkerSeverity.Info   ,'信息');

        if (!contentHtml)
            contentHtml = '<p style="text-align:center;color:var(--text-secondary);margin-top:20px;">没有符合当前过滤器的问题。</p>';

        content.innerHTML = contentHtml;

        /* 4. 确保面板展开 */
        toggleProblemsPanel(true);
    }

    function filterProblems(type) {
        if (problemsFilter === type) {
            // 再次点击同一个按钮时，隐藏问题面板
            problemsFilter = 'all';
            toggleProblemsPanel(false); // 关闭问题栏
            return;
        } else {
            problemsFilter = type;
        }

        // 重新渲染当前的markers
        if (monacoEditor) {
            const model = monacoEditor.getModel();
            const markers = monaco.editor.getModelMarkers({ resource: model.uri });
            renderProblemsPanel(markers);
        }
    }

    async function analyzeCurrentFileSyntax() {
        // 【暂时注释掉代码静态分析功能】
        // if (!monacoEditor || !currentEditingPath) return;
        // const ext = currentEditingPath.split('.').pop().toLowerCase();
        // let language = null;

        // if (['cpp', 'h', 'hpp', 'ino', 'c'].includes(ext)) {
        //     language = 'cpp';
        // } else if (ext === 'py') {
        //     language = 'python';
        // }

        // if (!language) {
        //     monaco.editor.setModelMarkers(monacoEditor.getModel(), 'syntax-checker', []);
        //     renderProblemsPanel([]);
        //     return;
        // }

        // const code = monacoEditor.getValue();
        // try {
        //     const payload = {
        //         code: code,
        //         language: language,
        //         filePath: currentEditingPath,
        //         workflowId: currentWorkflowId,
        //         localProjectId: syncedLocalProjectId
        //     };
        //     const result = await fetchApi('/projects/analyze-syntax', 'POST', payload);

        //     if (result && result.errors) {
        //         const model = monacoEditor.getModel();
        //         const modelLineCount = model.getLineCount();

        //         // 在映射前，先过滤掉无效和越界的错误
        //         const markers = result.errors
        //             .filter(err => err && typeof err.line === 'number' && err.line > 0 && err.line <= modelLineCount)
        //             .map(err => ({
        //                 message: err.message,
        //                 severity: err.severity === 'info' ? monaco.MarkerSeverity.Info : (err.severity === 'warning' ? monaco.MarkerSeverity.Warning : monaco.MarkerSeverity.Error),
        //                 startLineNumber: err.line,
        //                 startColumn: err.column || 1,
        //                 endLineNumber: err.line,
        //                 endColumn: model.getLineLength(err.line) + 1
        //             }));

        //         monaco.editor.setModelMarkers(model, 'syntax-checker', markers);
        //         renderProblemsPanel(markers);
        //     }

        // 清空问题面板，不显示任何语法错误
        if (monacoEditor) {
            monaco.editor.setModelMarkers(monacoEditor.getModel(), 'syntax-checker', []);
        }
        renderProblemsPanel([]);

        // } catch (error) {
        //     console.error("Syntax analysis request failed:", error);
        //     renderProblemsPanel([{
        //         message: `语法分析服务请求失败: ${error.message}`,
        //         startLineNumber: 1,
        //         startColumn: 1,
        //         severity: monaco.MarkerSeverity.Warning,
        //     }]);
        // }
    }

    async function saveFileContent() {
        if (!currentEditingPath || !monacoEditor) return;
        const saveBtn = document.getElementById('save-file-btn');

        saveBtn.textContent = '保存中...';
        saveBtn.disabled = true;

        try {
            const newContent = monacoEditor.getValue(); // 从Monaco Editor获取内容
            if (isLocalProject) {
                const handle = localFileHandles.get(currentEditingPath);
                if (handle && handle.kind === 'file') {
                    const writable = await handle.createWritable();
                    await writable.write(newContent);
                    await writable.close();
                } else { throw new Error('找不到文件句柄'); }
            } else {
                await fetchApi(`/workflows/${currentWorkflowId}/files?path=${encodeURIComponent(currentEditingPath)}`, 'PUT', { content: newContent });
            }

            saveBtn.textContent = '已保存!';
            setTimeout(() => { saveBtn.textContent = '保存'; saveBtn.disabled = false; }, 2000);
        } catch (error) {
            alert(`保存失败: ${error.message}`);
            saveBtn.textContent = '保存';
            saveBtn.disabled = false;
        }
    }

    async function flashFirmware() {
        // 检查是否有本地文件或云端项目
        const hasLocalFiles = document.getElementById('file-tree-container').children.length > 0;
        const hasCloudProject = !!currentWorkflowId;

        if (!hasCloudProject && !hasLocalFiles) {
            alert('请先打开一个项目');
            return;
        }

        const flashBtn = document.getElementById('flash-firmware-btn');
        const originalText = flashBtn.innerHTML;

        flashBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/></svg> 烧录中...';
        flashBtn.disabled = true;

        try {
            let result;
            if (hasLocalFiles && !hasCloudProject) {
                // 对于本地项目，尝试直接烧录
                const projectName = document.querySelector('#status-workflow span').textContent;

                if (syncedLocalProjectId) {
                    // 如果已同步，使用同步的项目ID
                    result = await fetchApi('/projects/flash', 'POST', {
                        localProjectId: syncedLocalProjectId
                    });
                } else {
                    // 尝试直接烧录（假设项目在temp_workspaces中）
                    const projectPath = `temp_workspaces/${projectName}`;
                    result = await fetchApi('/projects/flash', 'POST', {
                        localProjectPath: projectPath
                    });
                }
            } else {
                // 对于云端项目，使用工作流ID
                result = await fetchApi('/projects/flash', 'POST', {
                    workflowId: currentWorkflowId
                });
            }

            showFlashResult(true, result.message, '', result.output);
        } catch (error) {
            console.error('Flash error details:', error);

            // 尝试解析错误响应
            let errorMessage = error.message;
            let keyErrors = '';
            let fullOutput = '';
            let suggestions = [];

            if (error.responseData) {
                // 使用已解析的响应数据
                console.log('Raw error data from server:', error.responseData);
                console.log('Return code from server:', error.responseData.returncode);
                errorMessage = error.responseData.error || error.message;
                keyErrors = error.responseData.key_errors || '';
                fullOutput = error.responseData.output || '';
                suggestions = error.responseData.suggestions || [];
                console.log('Processed error data:', {errorMessage, keyErrors, fullOutput, suggestions});
                console.log('Key errors content:', keyErrors);
            } else if (error.response) {
                // 备用方案：使用已解析的响应数据
                if (error.responseData) {
                    console.log('Raw error data from server:', error.responseData);
                    errorMessage = error.responseData.error || error.message;
                    keyErrors = error.responseData.key_errors || '';
                    fullOutput = error.responseData.output || '';
                    suggestions = error.responseData.suggestions || [];
                    console.log('Processed error data:', {errorMessage, keyErrors, fullOutput, suggestions});
                } else {
                    errorMessage = `HTTP ${error.response.status}: ${error.response.statusText}`;
                }
            }

            showFlashResult(false, errorMessage, keyErrors, fullOutput, suggestions);
        } finally {
            flashBtn.innerHTML = originalText;
            flashBtn.disabled = false;
        }
    }


    // ===================================================================
    // Workflow Polling & Rendering (unchanged)
    // ===================================================================
    // ... (checkStatus, startPolling, stopPolling, etc. are unchanged)

    async function performWorkflowAction(workflowId, action) {
    // 【核心新增】一个专门用于发送操作的函数
    try {
        const actionButtonContainer = document.querySelector('#workflow-actions-container .actions');
        if(actionButtonContainer) actionButtonContainer.innerHTML = `<p>正在执行 ${action}...</p>`;

        // 【修复】在恢复工作流前，获取当前日志长度，避免重复显示历史日志
        try {
            const currentState = await fetchApi(`/workflows/${workflowId}`);
            if (currentState.logs) {
                const currentLogLines = currentState.logs.split('\n').filter(line => line.trim());
                window.lastLogCount = currentLogLines.length;
                console.log(`[RESUME] 设置日志计数器为: ${window.lastLogCount}，避免重复显示历史日志`);
            }
        } catch (e) {
            console.warn('获取当前日志状态失败:', e);
        }

        await fetchApi(`/workflows/${workflowId}/actions`, 'POST', { action: action }, 202);
        // Action is accepted, the polling will pick up the RUNNING state.
    } catch (error) {
        appendToWorkflowStream(`<div class="stream-entry status-failed">[ERROR] 执行操作失败: ${error.message}</div>`);
    }
    }

    async function checkStatus() {
        if (!currentWorkflowId || isLocalProject) return;
        try {
            const state = await fetchApi(`/workflows/${currentWorkflowId}`);
            renderWorkflowUpdate(state);
            if (['COMPLETED', 'FAILED'].includes(state.status)) {
                stopPolling();
                if (state.status === 'COMPLETED') {
                    // 【修复】添加工作流完成的视觉反馈，避免突兀的状态变化
                    appendToWorkflowStream(`<div class="stream-entry status-completed"><span class="timestamp">${formatTimestamp()}</span><span class="status-completed">[SUCCESS]</span> <span>🎉 项目构建完成！所有验证步骤已通过。</span></div>`);

                    // 【新增】显示工作流完成总结
                    showWorkflowCompletionSummary(state);

                    // 延迟切换到文件浏览器，让用户看到完成消息和总结
                    setTimeout(() => {
                        document.getElementById('btn-activity-files').click();
                        // 简单方式：直接加载文件树，让用户看到所有文件夹
                        loadAndRenderFileTree(currentWorkflowId);
                    }, 3000); // 3秒后切换到文件浏览器
                } else if (state.status === 'FAILED') {
                    // 【新增】为失败状态添加明确的反馈
                    appendToWorkflowStream(`<div class="stream-entry status-failed"><span class="timestamp">${formatTimestamp()}</span><span class="status-failed">[FAILED]</span> <span>❌ 工作流执行失败，请查看错误信息。</span></div>`);
                }

                // 【修复】延长延迟时间，给用户更多时间查看结果
                setTimeout(() => {
                    // 只有当前还是同一个工作流时才重置（避免用户已经开始新工作流的情况）
                    if (currentWorkflowId === state.workflow_id) {
                        console.log('工作流已完成，状态保持以供用户查看结果');
                        // 【改进】不自动重置状态，保留工作流结果供用户查看
                        // 用户可以通过点击 AI 工作流标签来开始新项目

                        // 添加一个提示，告诉用户可以开始新项目
                        if (state.status === 'COMPLETED') {
                            appendToWorkflowStream(`<div class="stream-entry"><span class="timestamp">${formatTimestamp()}</span><span class="status-info">[INFO]</span> <span>💡 您可以点击"AI工作流"标签开始新的项目开发。</span></div>`);
                        }
                    }
                }, 10000); // 10秒后显示提示
            }
        } catch (error) {
            stopPolling();
            appendToWorkflowStream(`<div class="stream-entry status-failed">[ERROR] 无法获取工作流状态: ${error.message}。轮询已停止。</div>`);
        }
    }

    function startPolling(workflowId) {
        stopPolling();
        currentWorkflowId = workflowId;
        checkStatus();
        statusInterval = setInterval(checkStatus, 1000);
    }

    function stopPolling() {
        if (statusInterval) clearInterval(statusInterval);
        statusInterval = null;
    }

    function formatTimestamp(date = new Date()) { return date.toLocaleTimeString('en-GB'); }

    // 【新增】显示工作流完成总结的函数
    function showWorkflowCompletionSummary(state) {
        const completedSteps = state.workflow_steps.filter(step => step.status === 'completed');
        const failedSteps = state.workflow_steps.filter(step => step.status === 'failed');

        let summaryHtml = `<div class="stream-entry status-info">
            <span class="timestamp">${formatTimestamp()}</span>
            <span class="status-info">[SUMMARY]</span>
            <span>📊 工作流执行总结：</span>
        </div>`;

        // 显示完成的步骤统计
        summaryHtml += `<div class="stream-entry">
            <span class="timestamp">${formatTimestamp()}</span>
            <span class="status-completed">[INFO]</span>
            <span>✅ 已完成步骤：${completedSteps.length} 个</span>
        </div>`;

        // 如果有失败的步骤，也显示出来
        if (failedSteps.length > 0) {
            summaryHtml += `<div class="stream-entry">
                <span class="timestamp">${formatTimestamp()}</span>
                <span class="status-failed">[INFO]</span>
                <span>❌ 失败步骤：${failedSteps.length} 个</span>
            </div>`;
        }

        // 显示关键验证步骤的结果
        const verificationSteps = ['deploy_and_verify_node', 'usb_upload_node', 'ota_deployment_node'];
        const completedVerificationSteps = completedSteps.filter(step => verificationSteps.includes(step.id));

        if (completedVerificationSteps.length > 0) {
            summaryHtml += `<div class="stream-entry status-completed">
                <span class="timestamp">${formatTimestamp()}</span>
                <span class="status-completed">[VERIFICATION]</span>
                <span>🔍 验证步骤已完成：${completedVerificationSteps.map(step => step.name).join(', ')}</span>
            </div>`;
        }

        appendToWorkflowStream(summaryHtml);
    }

    // 消息显示队列系统 - 统一处理所有消息
    let messageQueue = [];
    let isProcessingMessage = false;

    // 处理消息队列
    function processMessageQueue() {
        if (isProcessingMessage || messageQueue.length === 0) return;

        const nextMessage = messageQueue.shift();
        isProcessingMessage = true;

        // 先显示消息框架
        appendToWorkflowStream(nextMessage.html);

        if (nextMessage.needsTyping) {
            // AI消息需要打字效果
            setTimeout(() => {
                startTypewriterEffect(nextMessage.entryId, nextMessage.text, () => {
                    isProcessingMessage = false;
                    // 打字完成后，处理队列中的下一个项目
                    setTimeout(() => {
                        processMessageQueue();
                    }, 300); // 每条消息之间300ms间隔
                });
            }, 100);
        } else {
            // 系统消息直接显示，但也要等待一段时间
            setTimeout(() => {
                isProcessingMessage = false;
                setTimeout(() => {
                    processMessageQueue();
                }, 500); // 系统消息显示500ms后处理下一个
            }, 100);
        }
    }

    // 添加消息到队列
    function addToMessageQueue(html, entryId = null, text = null, needsTyping = false) {
        messageQueue.push({ html, entryId, text, needsTyping });
        processMessageQueue();
    }

    // 打字机效果函数
    function startTypewriterEffect(entryId, text, onComplete) {
        const entry = document.getElementById(entryId);
        if (!entry) {
            if (onComplete) onComplete();
            return;
        }

        const textElement = entry.querySelector('.typewriter-text');
        const spinner = entry.querySelector('.thinking-spinner');

        if (!textElement) {
            if (onComplete) onComplete();
            return;
        }

        textElement.classList.add('typing');
        textElement.textContent = '';

        let index = 0;
        const speed = 40; // 稍微加快打字速度

        function typeNextChar() {
            if (index < text.length) {
                textElement.textContent += text.charAt(index);
                index++;
                setTimeout(typeNextChar, speed);
            } else {
                // 打字完成，移除光标和旋转圆圈
                textElement.classList.remove('typing');
                textElement.style.borderRight = 'none'; // 确保光标完全移除
                if (spinner) {
                    spinner.remove(); // 完全移除旋转圆圈元素
                }

                // 调用完成回调
                if (onComplete) onComplete();
            }
        }

        typeNextChar();
    }

    // 创建气泡粒子效果 - 增强版
    function createBubbleParticles(container) {
        const particleCount = 12; // 增加粒子数量

        for (let i = 0; i < particleCount; i++) {
            setTimeout(() => {
                const particle = document.createElement('div');
                const particleType = (i % 3) + 1; // 循环使用3种粒子类型
                particle.className = `particle type-${particleType}`;

                // 更自然的位置分布（围绕气泡周围）
                const angle = (i / particleCount) * 2 * Math.PI + Math.random() * 0.5;
                const radius = 30 + Math.random() * 40;
                const x = 50 + Math.cos(angle) * (radius / 100) * 50; // 转换为百分比
                const y = 120 + Math.sin(angle) * radius;

                particle.style.left = x + '%';
                particle.style.bottom = y + 'px';

                // 随机延迟，让粒子错开出现
                particle.style.animationDelay = (Math.random() * 0.8) + 's';

                container.appendChild(particle);

                // 动画结束后移除粒子
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.remove();
                    }
                }, 2500);
            }, i * 80); // 稍微缩短错开时间，让效果更连贯
        }
    }

    function appendToWorkflowStream(htmlContent) {
        const container = document.getElementById('workflow-stream-container');
        container.insertAdjacentHTML('beforeend', htmlContent);
        container.scrollTop = container.scrollHeight;
    }

    // 在 <script> 标签内，用这个新函数完整替换旧的 renderWorkflowUpdate 函数
function renderWorkflowUpdate(state) {
    document.querySelector('#status-workflow span').textContent = state.status || 'N/A';
    const currentTask = state.current_device_task;
    document.querySelector('#status-device span').textContent = currentTask ? currentTask.device_role : '无设备';

    const actionsContainer = document.getElementById('deployment-actions-container');

    // 1. 处理部署按钮的显示/隐藏
    if (state.status === 'PAUSED' && state.available_actions?.length > 0) {
        let buttonsHtml = '';
        if (state.available_actions.includes('DEPLOY_USB')) {
            buttonsHtml += `
                <button class="deploy-action-btn secondary" onclick="postDeploymentAction('DEPLOY_USB')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20v-4"/><path d="M12 10V4"/><path d="M8 16H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h4"/><path d="m16 4 h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-4"/><path d="M8 12h8"/></svg>
                    USB 部署
                </button>`;
        }
        if (state.available_actions.includes('DEPLOY_OTA')) {
            buttonsHtml += `
                <button class="deploy-action-btn secondary" onclick="postDeploymentAction('DEPLOY_OTA')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12a7 7 0 1 1 14 0"/><path d="M8.5 12.5a4 4 0 1 1 7 0"/><path d="M12 17.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z"/></svg>
                    OTA 部署
                </button>`;
        }
        actionsContainer.innerHTML = buttonsHtml;
        actionsContainer.style.display = 'flex';
    } else {
        actionsContainer.style.display = 'none';
    }

    const existingActionsContainer = document.getElementById('workflow-actions-container');
    if (existingActionsContainer) {
        existingActionsContainer.remove();
    }

    if (state.status === 'PAUSED' && state.available_actions && state.available_actions.length > 0) {
        let actionsHtml = '<div id="workflow-actions-container" class="status-actions-container">';
        actionsHtml += '<p>等待操作：编译完成，请选择部署方式</p><div class="actions">';

        state.available_actions.forEach(action => {
            const actionText = action === 'DEPLOY_USB' ? 'USB 部署' : (action === 'DEPLOY_OTA' ? 'OTA 部署' : action);
            actionsHtml += `<button onclick="performWorkflowAction('${state.workflow_id}', '${action}')">${actionText}</button>`;
        });
        actionsHtml += '</div></div>';
        appendToWorkflowStream(actionsHtml);
    }

    // 2. 处理实时日志显示（智能日志系统）
    if (state.logs && typeof state.logs === 'string') {
        console.log('收到日志数据:', state.logs.length, '字符'); // 调试信息
        const logLines = state.logs.split('\n').filter(line => line.trim());
        const currentLogCount = logLines.length;

        console.log('日志行数:', currentLogCount, '上次计数:', window.lastLogCount || 0); // 调试信息

        // 只显示新增的日志行，使用队列系统确保顺序显示
        if (currentLogCount > (window.lastLogCount || 0)) {
            const newLines = logLines.slice(window.lastLogCount || 0);
            console.log('新增日志行:', newLines); // 调试信息

            // 将新行添加到处理队列
            newLines.forEach(line => {
                processLogLine(line);
            });

            window.lastLogCount = currentLogCount;
        }
    }

    // 处理单行日志的函数
    function processLogLine(line) {
        if (line.trim()) {
            console.log('处理日志行:', line); // 调试信息

            // 解析日志行格式: [时间戳] emoji 消息
            const logMatch = line.match(/^\[(\d{2}:\d{2}:\d{2})\]\s*(.+)$/);
            if (logMatch) {
                const timestamp = logMatch[1];
                const message = logMatch[2];

                console.log('解析成功 - 时间戳:', timestamp, '消息:', message); // 调试信息

                // 检测是否是AI说的话（包含emoji）还是系统状态消息
                const isAIMessage = /[💭🏗️📝🔧❌🔍🛠️✅ℹ️⚠️]/.test(message);

                // 现在不过滤"步骤 'xxx' 已完成"消息了，因为绿色的COMPLETED消息已经在工作流步骤渲染中被过滤掉了

                // 检测日志类型并应用相应样式
                let logClass = 'status-running';
                if (message.includes('💭')) {
                    logClass = 'status-thinking';
                } else if (message.includes('🏗️')) {
                    logClass = 'status-coding';
                } else if (message.includes('📝')) {
                    logClass = 'status-coding';
                } else if (message.includes('🔧')) {
                    logClass = 'status-compiling';
                } else if (message.includes('❌')) {
                    logClass = 'status-failed';
                } else if (message.includes('🔍')) {
                    logClass = 'status-analyzing';
                } else if (message.includes('🛠️')) {
                    logClass = 'status-fixing';
                } else if (message.includes('✅')) {
                    logClass = 'status-completed';
                }

                if (isAIMessage) {
                    // AI消息：使用打字机效果，加入队列
                    const entryId = `log-entry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                    const entryHtml = `<div class="stream-entry" id="${entryId}"><span class="timestamp">${timestamp}</span><span class="${logClass}">[AI]</span> <span class="typewriter-text" data-text="${escapeHtml(message)}"></span><span class="thinking-spinner"></span></div>`;
                    console.log('添加AI日志条目到队列:', message); // 调试信息

                    // 添加到消息队列，需要打字效果
                    addToMessageQueue(entryHtml, entryId, message, true);
                } else {
                    // 系统状态消息：不使用打字机效果，但也要排队显示
                    const entryHtml = `<div class="stream-entry"><span class="timestamp">${timestamp}</span><span class="${logClass}">[SYSTEM]</span> <span>${escapeHtml(message)}</span></div>`;
                    console.log('添加系统日志条目到队列:', message); // 调试信息

                    // 添加到消息队列，不需要打字效果
                    addToMessageQueue(entryHtml, null, null, false);
                }
            } else {
                console.log('日志行格式不匹配:', line); // 调试信息
                // 如果格式不匹配，也加入队列显示，保持顺序
                const entryHtml = `<div class="stream-entry"><span class="timestamp">${formatTimestamp()}</span><span class="status-running">[LOG]</span> <span>${escapeHtml(line)}</span></div>`;
                addToMessageQueue(entryHtml, null, null, false);
            }
        }
    }

    if (!state.workflow_steps) return;

    // 3. 处理工作流步骤的状态渲染
    state.workflow_steps.forEach(step => {
        const stepStateKey = `${step.id}-${step.status}`;
        if (lastRenderedStepState[stepStateKey] && ['completed', 'failed'].includes(step.status)) {
            return;
        }

        // 【核心修正】在编译成功时，自动跳转到文件浏览器
        if (step.id === 'compile_node' && step.status === 'completed' && !lastRenderedStepState[stepStateKey]) {
            const hasSwitched = document.body.dataset.switchedToFileView === currentWorkflowId;
            if (!hasSwitched) {
                console.log("Compilation successful. Switching to file explorer view.");
                document.getElementById('btn-activity-files').click();
                // 简单方式：直接加载文件树，让用户看到所有文件夹
                loadAndRenderFileTree(currentWorkflowId);
                document.body.dataset.switchedToFileView = currentWorkflowId;
            }
        }

        let entryHtml = '';
        const timestamp = formatTimestamp();
        const statusClassMap = {
            'running': 'status-running', 'completed': 'status-completed',
            'failed': 'status-failed', 'paused': 'status-warn'
        };
        const statusText = (step.status || 'pending').toUpperCase();
        const statusClass = statusClassMap[step.status] || '';

        if (step.status !== 'pending' && !lastRenderedStepState[stepStateKey]) {
            // 【修复】对于验证相关的步骤，显示完成状态以提供更好的用户反馈
            const verificationSteps = ['deploy_and_verify_node', 'usb_upload_node', 'ota_deployment_node'];
            const shouldShowCompleted = verificationSteps.includes(step.id) || step.status === 'failed';

            if (step.status === 'completed' && !shouldShowCompleted) {
                // 跳过非验证步骤的COMPLETED状态显示，避免冗余的绿色消息
                lastRenderedStepState[stepStateKey] = true;
                return;
            }

             // 【改进】为验证步骤添加更详细的状态信息
             let stepMessage = step.name;
             if (step.id === 'deploy_and_verify_node' && step.status === 'completed') {
                 stepMessage += ' - 设备验证通过 ✅';
             } else if (step.id === 'usb_upload_node' && step.status === 'completed') {
                 stepMessage += ' - USB部署成功 🔌';
             } else if (step.id === 'ota_deployment_node' && step.status === 'completed') {
                 stepMessage += ' - OTA部署成功 📡';
             } else if (step.status === 'running') {
                 stepMessage += '...';
             }

             entryHtml = `<div class="stream-entry ${statusClass}"><span class="timestamp">${timestamp}</span><span class="${statusClass}">[${statusText}]</span> <span>${escapeHtml(stepMessage)}</span></div>`;
             if (step.status === 'completed' || step.status === 'failed') {
                if (step.output) {
                    const formattedOutput = escapeHtml(step.output);
                    entryHtml += `<div class="output-block"><div class="output-block-header">产出物: ${step.name}</div><div class="output-block-content"><pre><code>${formattedOutput}</code></pre></div></div>`;
                }
                if (step.status === 'failed' && step.log) {
                    entryHtml += `<div class="output-block"><div class="output-block-header">错误日志:</div><div class="output-block-content"><pre><code>${escapeHtml(step.log)}</code></pre></div></div>`;
                }
             }
             appendToWorkflowStream(entryHtml);
             lastRenderedStepState[stepStateKey] = true;
        }
    });
}
    // ===================================================================
    // Initial Setup
    // ===================================================================

    function setupActivityBar() {
        // This function remains the same as the previous version
        const buttons = document.querySelectorAll('#activity-bar .activity-group:first-child .activity-btn');
        const views = document.querySelectorAll('#main-view .view');
        const buttonToViewMap = {
            'btn-activity-ai': 'view-ai-workflow',
            'btn-activity-files': 'view-file-explorer',
            'btn-activity-devices': 'view-device-manager'
        };

        const viewActivationLogic = {
            'view-ai-workflow': async () => {
                // 检查是否有活跃的工作流（正在运行的）
                let hasActiveWorkflow = false;
                if (currentWorkflowId && !isLocalProject) {
                    try {
                        const state = await fetchApi(`/workflows/${currentWorkflowId}`);
                        hasActiveWorkflow = !['COMPLETED', 'FAILED'].includes(state.status);
                    } catch (error) {
                        // 如果获取状态失败，假设没有活跃工作流
                        hasActiveWorkflow = false;
                        currentWorkflowId = null;
                    }
                }

                // 【修复界面偏移问题】获取工作流视图容器
                const workflowView = document.getElementById('view-ai-workflow');

                // 【改进】根据工作流状态管理布局类，保留已完成工作流的显示
                if (hasActiveWorkflow) {
                    // 有活跃工作流时，添加workflow-active类以使用工作流布局
                    workflowView.classList.add('workflow-active');
                } else if (currentWorkflowId) {
                    // 【修复】如果有已完成的工作流，保持workflow-active布局以显示结果
                    try {
                        const completedState = await fetchApi(`/workflows/${currentWorkflowId}`);
                        if (['COMPLETED', 'FAILED'].includes(completedState.status)) {
                            workflowView.classList.add('workflow-active');
                            console.log('保持已完成工作流的显示布局');
                        } else {
                            workflowView.classList.remove('workflow-active');
                        }
                    } catch (error) {
                        // 如果获取失败，恢复默认布局
                        workflowView.classList.remove('workflow-active');
                    }
                } else {
                    // 没有工作流时，移除workflow-active类以恢复默认居中布局
                    workflowView.classList.remove('workflow-active');
                }

                initialPromptContainer.style.display = hasActiveWorkflow ? 'none' : 'flex';
                workflowStreamContainer.style.display = hasActiveWorkflow ? 'block' : 'none';
                if (hasActiveWorkflow) checkStatus();
                renderProblemsPanel([]);

                // --- 从这里开始添加 ---
                // 如果动画已经播放过，就切换到静态显示样式
                if (typeof welcomeAnimationPlayed !== 'undefined' && welcomeAnimationPlayed) {
                    const greetingEl = document.getElementById('welcome-greeting');
                    if (greetingEl) {
                        greetingEl.classList.remove('typing-active');
                        greetingEl.classList.add('static-greeting');
                    }
                }
                // --- 到这里结束添加 ---
            },
            'view-file-explorer': () => {
                const treeContainer = document.getElementById('file-tree-container');
                const flashBtn = document.getElementById('flash-firmware-btn');

                // 检查文件树容器中是否有内容，如果有且不是错误信息，说明有本地项目
                const hasLocalFiles = treeContainer.children.length > 0 &&
                                     !treeContainer.textContent.includes('没有活动的构建任务') &&
                                     !treeContainer.textContent.includes('加载文件中') &&
                                     !treeContainer.textContent.includes('文件夹为空');

                // 修复：如果检测到有本地文件但状态不正确，恢复状态
                if (hasLocalFiles && !isLocalProject && !currentWorkflowId) {
                    console.log('Detected local project from file tree, restoring state...');
                    isLocalProject = true;
                }

                // 检查是否有活动项目（云端项目或本地项目）
                const hasActiveProject = currentWorkflowId || isLocalProject;



                if (!hasActiveProject) {
                     if (!hasLocalFiles) {
                         treeContainer.innerHTML = '<p style="padding: 10px; color: var(--text-secondary);">没有活动的构建任务。请先开始一个新项目或打开/加载一个项目。</p>';
                     }
                     flashBtn.style.display = 'none';
                } else {
                    // 有活动项目时显示烧录按钮
                    flashBtn.style.display = 'inline-flex';
                }

                if (!currentEditingPath) {
                    renderProblemsPanel([]);
                }
            },
            'view-device-manager': () => {
                loadUserDevices();
                // 新增：切换到此视图时，隐藏问题面板
                renderProblemsPanel([]);
            }
        };

        buttons.forEach(button => {
            button.addEventListener('click', () => {
                const targetViewId = buttonToViewMap[button.id];
                if (!targetViewId) return;



                buttons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                views.forEach(view => view.classList.toggle('active', view.id === targetViewId));

                if (viewActivationLogic[targetViewId]) {
                    try { viewActivationLogic[targetViewId](); }
                    catch (e) { console.error(`Error activating view ${targetViewId}:`, e); }
                }
            });
        });
    }

    function setupThemeToggle() {
        // This function remains the same as the previous version
        const toggleButton = document.getElementById('btn-theme-toggle');
        const sunIcon = document.querySelector('.theme-icon-sun');
        const moonIcon = document.querySelector('.theme-icon-moon');
        const applyTheme = (theme) => {
            if (theme === 'light') {
                document.documentElement.setAttribute('data-theme', 'light');
                sunIcon.style.display = 'none'; moonIcon.style.display = 'block';
            } else {
                document.documentElement.removeAttribute('data-theme');
                sunIcon.style.display = 'block'; moonIcon.style.display = 'none';
            }
            // 联动更新Monaco主题
            if (monacoEditor) {
                defineAndSetMonacoTheme();
            }
        };
        toggleButton.addEventListener('click', () => {
            const newTheme = document.documentElement.hasAttribute('data-theme') ? 'dark' : 'light';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
        });
        applyTheme(localStorage.getItem('theme') || 'dark');
    }

    function setupSettings() {
        // This function remains the same as the previous version
        const settingsButton = document.getElementById('btn-settings');
        const settingsDialog = document.getElementById('settings-dialog');
        const settingsForm = document.getElementById('settings-form');
        settingsButton.addEventListener('click', async () => {
            settingsForm.reset();
            updateMessage('settings-message', '', false);
            try {
                const config = await fetchApi('/user/config');
                document.getElementById('wifi-ssid').value = config.wifi_ssid || '';
                document.getElementById('wifi-password').value = config.wifi_password || '';
            } catch (error) { updateMessage('settings-message', '无法加载配置', true); }
            settingsDialog.showModal();
        });
        settingsForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            const payload = {
                wifi_ssid: document.getElementById('wifi-ssid').value,
                wifi_password: document.getElementById('wifi-password').value
            };
            try {
                await fetchApi('/user/config', 'PUT', payload);
                updateMessage('settings-message', '设置已保存', false);
                setTimeout(() => settingsDialog.close(), 1500);
            } catch (error) { updateMessage('settings-message', error.message, true); }
        });
    }


        function setupPanelResizer() {
            const resizer = document.getElementById('panel-resizer');
            const panel = document.getElementById('problems-panel');
            const mainView = document.getElementById('main-view');

            let startY, startHeight;

            function startResize(e) {
                startY = e.clientY;
                startHeight = parseInt(document.defaultView.getComputedStyle(panel).height, 10);
                window.addEventListener('mousemove', doResize);
                window.addEventListener('mouseup', stopResize);
                document.body.style.userSelect = 'none';
                document.body.style.cursor = 'ns-resize'; // 添加全局鼠标样式
            }

            function doResize(e) {
                const newHeight = startHeight - (e.clientY - startY);
                // 设置合理的最小/最大高度
                if (newHeight > 100 && newHeight < window.innerHeight * 0.5) {
                    panel.style.height = newHeight + 'px';
                }
            }

            function stopResize() {
                window.removeEventListener('mousemove', doResize);
                window.removeEventListener('mouseup', stopResize);
                document.body.style.userSelect = '';
                document.body.style.cursor = ''; // 恢复鼠标样式
            }

            resizer.addEventListener('mousedown', startResize);
        }
        window.closeProblemsPanel  = closeProblemsPanel;
        window.toggleProblemsPanel = toggleProblemsPanel;
        window.filterProblems      = filterProblems;
        // --- Monaco Editor 初始化 ---
        require.config({ paths: { 'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.47.0/min/vs' }});
        require(['vs/editor/editor.main'], function() {
            try {
                // 直接赋值给外部声明的变量
                monacoEditor = monaco.editor.create(document.getElementById('monaco-editor-container'), {
                    value: '',
                    language: 'plaintext',
                    automaticLayout: true,
                    fontSize: 15,
                    fontFamily: 'JetBrains Mono, Fira Code, monospace',
                    lineHeight: 1.7,
                    minimap: { enabled: false }, // 默认关闭小地图以节省空间
                    // 禁用一些可能导致键盘事件问题的功能
                    contextmenu: false,
                    quickSuggestions: false,
                    parameterHints: { enabled: false },
                    suggestOnTriggerCharacters: false
                });

                // 初始化并应用自定义主题
                defineAndSetMonacoTheme();

                // --- 新增：实时语法检查的防抖触发器 ---
                let debounceTimer;
                monacoEditor.onDidChangeModelContent(() => {
                    try {
                        // 清除上一个计时器
                        clearTimeout(debounceTimer);

                        // 设置一个新的计时器，在用户停止输入1500毫秒（1.5秒）后触发
                        debounceTimer = setTimeout(() => {
                            analyzeCurrentFileSyntax();
                        }, 1500);
                    } catch (error) {
                        console.error('Monaco Editor content change handler error:', error);
                    }
                });
            } catch (error) {
                console.error('Monaco Editor initialization error:', error);
                // 如果Monaco Editor初始化失败，显示错误信息
                document.getElementById('monaco-editor-container').innerHTML =
                    '<div style="padding: 20px; color: var(--accent-error);">编辑器初始化失败，请刷新页面重试</div>';
            }
        });
        // Cache DOM elements
        loginView = document.getElementById('login-view');
        ideView = document.getElementById('ide-view');
        workflowStreamContainer = document.getElementById('workflow-stream-container');
        initialPromptContainer = document.getElementById('initial-prompt-container');
        deviceGrid = document.getElementById('device-grid');

        // Setup UI handlers
        setupActivityBar();
        setupThemeToggle();
        setupSettings();
        setupPanelResizer();
        setupSidebarResizer();

        // ===================================================================
        // Smart Peripheral Configuration Functions
        // ===================================================================

        let currentSmartPeripheralStep = 1;
        let selectedPeripheralFunction = null;
        let selectedImplementationType = null;
        let selectedComponent = null;
        let currentTaskIndex = null;

        // 保存表单数据的对象
        let savedFormData = {
            componentIndex: null,
            pinValues: {},
            configValues: {}
        };

        // 保存第3步的表单数据
        function saveStep3FormData() {
            if (currentSmartPeripheralStep !== 3) return;

            // 保存组件选择
            const componentSelect = document.getElementById('component-select');
            if (componentSelect) {
                savedFormData.componentIndex = componentSelect.value;
            }

            // 保存引脚配置
            savedFormData.pinValues = {};
            if (selectedComponent && selectedComponent.pins) {
                selectedComponent.pins.forEach((pin, index) => {
                    const pinInput = document.getElementById(`pin-${index}`);
                    if (pinInput) {
                        savedFormData.pinValues[index] = pinInput.value;
                    }
                });
            }

            // 保存附加配置
            savedFormData.configValues = {};
            if (selectedComponent && selectedComponent.config) {
                selectedComponent.config.forEach((config, index) => {
                    const configInput = document.getElementById(`config-${index}`);
                    if (configInput) {
                        savedFormData.configValues[index] = configInput.value;
                    }
                });
            }
        }

        // 恢复第3步的表单数据
        function restoreStep3FormData() {
            if (currentSmartPeripheralStep !== 3) return;

            // 恢复组件选择
            if (savedFormData.componentIndex !== null) {
                const componentSelect = document.getElementById('component-select');
                if (componentSelect) {
                    componentSelect.value = savedFormData.componentIndex;
                    // 触发change事件以生成引脚配置
                    const event = new Event('change');
                    componentSelect.dispatchEvent(event);

                    // 延迟恢复引脚和配置数据，确保DOM已更新
                    setTimeout(() => {
                        // 恢复引脚配置
                        Object.keys(savedFormData.pinValues).forEach(index => {
                            const pinInput = document.getElementById(`pin-${index}`);
                            if (pinInput) {
                                pinInput.value = savedFormData.pinValues[index];
                            }
                        });

                        // 恢复附加配置
                        Object.keys(savedFormData.configValues).forEach(index => {
                            const configInput = document.getElementById(`config-${index}`);
                            if (configInput) {
                                configInput.value = savedFormData.configValues[index];
                            }
                        });
                    }, 50);
                }
            }
        }

        function openSmartPeripheralDialog(skipReset = false) {
            // 只在非编辑模式下重置状态
            if (!skipReset) {
                currentSmartPeripheralStep = 1;
                selectedPeripheralFunction = null;
                selectedImplementationType = null;
                selectedComponent = null;

                // 清空保存的表单数据
                savedFormData = {
                    componentIndex: null,
                    pinValues: {},
                    configValues: {}
                };

                // 重置表单
                document.getElementById('smart-peripheral-form').reset();

                // 显示第一步，隐藏其他步骤
                showSmartPeripheralStep(1);
            }

            // 清空错误信息
            document.getElementById('smart-peripheral-error').textContent = '';

            // 显示对话框
            document.getElementById('smart-peripheral-dialog').showModal();
        }

        function showSmartPeripheralStep(step) {
            // 隐藏所有步骤
            for (let i = 1; i <= 3; i++) {
                document.getElementById(`step-${i}`).style.display = 'none';
            }

            // 显示当前步骤
            document.getElementById(`step-${step}`).style.display = 'block';
            currentSmartPeripheralStep = step;

            // 更新按钮状态
            const prevBtn = document.getElementById('smart-peripheral-prev-btn');
            const nextBtn = document.getElementById('smart-peripheral-next-btn');
            const submitBtn = document.getElementById('smart-peripheral-submit-btn');

            prevBtn.style.display = step > 1 ? 'inline-block' : 'none';
            nextBtn.style.display = step < 3 ? 'inline-block' : 'none';
            submitBtn.style.display = step === 3 ? 'inline-block' : 'none';
        }

        function smartPeripheralNextStep() {
            const errorDiv = document.getElementById('smart-peripheral-error');
            errorDiv.textContent = '';

            if (currentSmartPeripheralStep === 1) {
                // 验证第1步：选择功能
                const functionSelect = document.getElementById('peripheral-function');
                if (!functionSelect.value) {
                    errorDiv.textContent = '请选择要实现的功能';
                    return;
                }

                selectedPeripheralFunction = functionSelect.value;

                // 更新第2步的选项
                updateImplementationTypeOptions();
                showSmartPeripheralStep(2);

            } else if (currentSmartPeripheralStep === 2) {
                // 验证第2步：选择实现方式
                const implementationSelect = document.getElementById('implementation-type');
                if (!implementationSelect.value) {
                    errorDiv.textContent = '请选择实现方式';
                    return;
                }

                selectedImplementationType = implementationSelect.value;

                // 生成第3步的动态配置
                generateDynamicConfig();
                showSmartPeripheralStep(3);

                // 恢复之前保存的表单数据
                setTimeout(() => {
                    restoreStep3FormData();
                }, 100);
            }
        }

        function smartPeripheralPrevStep() {
            if (currentSmartPeripheralStep > 1) {
                // 在切换步骤前保存当前步骤的数据
                if (currentSmartPeripheralStep === 3) {
                    saveStep3FormData();
                }
                showSmartPeripheralStep(currentSmartPeripheralStep - 1);
            }
        }

        function updateImplementationTypeOptions(functionKey) {
            // 如果没有传递参数，使用全局变量
            const targetFunction = functionKey || selectedPeripheralFunction;

            const implementationSelect = document.getElementById('implementation-type');

            if (!targetFunction) {
                console.error('功能类型未指定');
                return;
            }

            const functionData = peripheralDatabase[targetFunction];

            if (!functionData) {
                console.error('无效的功能类型:', targetFunction);
                return;
            }

            // 清空现有选项
            implementationSelect.innerHTML = '<option value="">-- 请选择实现方式 --</option>';

            // 根据功能添加可用的实现方式
            if (functionData.specificModels && functionData.specificModels.length > 0) {
                implementationSelect.innerHTML += '<option value="SPECIFIC_MODEL">使用特定型号的模块</option>';
            }

            if (functionData.genericComponents && functionData.genericComponents.length > 0) {
                implementationSelect.innerHTML += '<option value="GENERIC_COMPONENT">使用通用电子元件</option>';
            }
        }

        function generateDynamicConfig() {
            const container = document.getElementById('dynamic-config-container');
            const functionData = peripheralDatabase[selectedPeripheralFunction];

            if (!functionData) {
                console.error('无效的功能类型:', selectedPeripheralFunction);
                container.innerHTML = '<p style="color: var(--accent-error);">配置加载失败</p>';
                return;
            }

            let components = [];
            if (selectedImplementationType === 'SPECIFIC_MODEL') {
                components = functionData.specificModels || [];
            } else {
                components = functionData.genericComponents || [];
            }

            if (components.length === 0) {
                console.error('没有可用的组件:', selectedImplementationType, functionData);
                container.innerHTML = '<p style="color: var(--accent-error);">没有可用的组件</p>';
                return;
            }

            let html = `
                <div class="form-group">
                    <label for="component-select">${selectedImplementationType === 'SPECIFIC_MODEL' ? '具体型号' : '元件类型'}</label>
                    <select id="component-select" required onchange="updatePinConfiguration()">
                        <option value="">-- 请选择 --</option>
            `;

            components.forEach((component, index) => {
                html += `<option value="${index}">${component.name} - ${component.description}</option>`;
            });

            html += `
                    </select>
                </div>
                <div id="pin-config-container" style="display: none;">
                    <h4 style="color: var(--primary-color); margin: 15px 0 10px 0;">引脚配置</h4>
                    <div id="pin-inputs-container"></div>
                </div>
                <div id="additional-config-container" style="display: none;">
                    <h4 style="color: var(--primary-color); margin: 15px 0 10px 0;">附加配置</h4>
                    <div id="additional-inputs-container"></div>
                </div>

            `;

            container.innerHTML = html;
        }

        function updatePinConfiguration() {
            const componentSelect = document.getElementById('component-select');
            const componentIndex = parseInt(componentSelect.value);

            if (isNaN(componentIndex)) {
                document.getElementById('pin-config-container').style.display = 'none';
                document.getElementById('additional-config-container').style.display = 'none';
                selectedComponent = null; // 重置选择
                return;
            }

            const functionData = peripheralDatabase[selectedPeripheralFunction];
            if (!functionData) {
                console.error('无效的功能类型:', selectedPeripheralFunction);
                return;
            }

            const componentsArray = selectedImplementationType === 'SPECIFIC_MODEL'
                ? functionData.specificModels
                : functionData.genericComponents;

            if (!componentsArray || !Array.isArray(componentsArray)) {
                console.error('组件数组不存在:', selectedImplementationType, functionData);
                return;
            }

            let components = componentsArray;

            if (componentIndex >= components.length) {
                console.error('组件索引超出范围:', componentIndex, components.length);
                return;
            }

            selectedComponent = components[componentIndex];

            // 生成引脚配置输入框
            generatePinInputs();

            // 生成附加配置输入框
            generateAdditionalConfig();
        }

        function openSmartPeripheralDialogForTask(taskIndex) {
            currentTaskIndex = taskIndex;
            openSmartPeripheralDialog();
        }

        function submitSmartPeripheral() {
            const errorDiv = document.getElementById('smart-peripheral-error');
            errorDiv.textContent = '';

            // 验证必填字段
            const componentSelect = document.getElementById('component-select');

            if (!componentSelect.value) {
                errorDiv.textContent = '请选择具体的组件或型号';
                return;
            }

            // 收集引脚配置
            const pins = [];
            selectedComponent.pins.forEach((pin, index) => {
                const pinInput = document.getElementById(`pin-${index}`);
                if (pin.required && !pinInput.value) {
                    errorDiv.textContent = `请填写必需的引脚：${pin.name}`;
                    return;
                }

                if (pinInput.value) {
                    pins.push({
                        name: pin.name,
                        number: parseInt(pinInput.value),
                        description: pin.description
                    });
                }
            });

            if (errorDiv.textContent) return; // 如果有错误，停止执行

            // 收集附加配置
            const additionalConfig = {};
            if (selectedComponent.config) {
                selectedComponent.config.forEach((config, index) => {
                    const configInput = document.getElementById(`config-${index}`);
                    if (configInput.value) {
                        additionalConfig[config.name] = configInput.value;
                    }
                });
            }

            // 构建外设数据
            const peripheralData = {
                function: selectedPeripheralFunction,
                implementationType: selectedImplementationType,
                name: selectedComponent.name, // 直接使用组件名称
                model: selectedComponent.name,
                interface: selectedComponent.interface,
                pins: pins,
                config: additionalConfig,
                description: selectedComponent.description
            };

            // 根据上下文添加到不同的位置
            if (editingPeripheralElement) {
                // 编辑模式：替换现有外设
                replacePeripheralElement(peripheralData);
                editingPeripheralElement = null; // 重置
            } else if (currentTaskIndex !== null) {
                addPeripheralToTaskForm(peripheralData, currentTaskIndex);
                currentTaskIndex = null; // 重置
            } else {
                addPeripheralToDeviceForm(peripheralData);
            }

            // 关闭对话框
            document.getElementById('smart-peripheral-dialog').close();
        }

        function addPeripheralToTaskForm(peripheralData, taskIndex) {
            const container = document.querySelector(`.peripheral-container[data-task-index='${taskIndex}']`);
            const configAttr = `data-config='${escapeHtml(JSON.stringify(peripheralData))}'`;

            const newPeripheralHtml = `
                <div class="peripheral-item" ${configAttr} style="display: grid; grid-template-columns: 1.5fr 1.5fr 2.5fr auto; gap: 10px; align-items: start; margin-bottom: 10px; background: var(--card-background); border: 1px solid var(--border-color); border-radius: 6px; padding: 10px;">
                    <div style="font-weight: bold; color: var(--text-primary);">${peripheralData.name}</div>
                    <div style="color: var(--text-secondary);">${peripheralData.model}</div>
                    <div class="pins-container" style="width: 100%; overflow: hidden;">
                        ${(peripheralData.pins && Array.isArray(peripheralData.pins)) ? peripheralData.pins.map(pin => `
                            <div class="pin-item" style="display: flex; gap: 8px; margin-bottom: 5px; align-items: center; width: 100%;">
                                <span style="width: 45%; min-width: 50px; font-size: 0.85em; color: var(--text-secondary);">${pin && pin.name ? pin.name : '未知引脚'}:</span>
                                <span style="width: 35%; min-width: 40px; font-size: 0.85em; font-weight: bold;">GPIO ${pin && pin.number ? pin.number : '?'}</span>
                            </div>
                        `).join('') : '<div style="color: var(--text-secondary); font-size: 0.85em;">无引脚配置</div>'}
                    </div>
                    <div style="display: flex; gap: 5px;">
                        <button type="button" class="edit-peripheral-btn" onclick="editPeripheral(this)" title="编辑外设" style="background: var(--primary-color); color: white; border: none; border-radius: 4px; padding: 5px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.8'" onmouseout="this.style.opacity='1'">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                        </button>
                        <button type="button" class="remove-peripheral-btn" onclick="this.closest('.peripheral-item').remove()" title="删除外设" style="background: var(--accent-error); color: white; border: none; border-radius: 4px; padding: 5px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--accent-error-hover)'" onmouseout="this.style.background='var(--accent-error)'">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                        </button>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', newPeripheralHtml);

            // 显示成功消息
            const successMsg = document.createElement('div');
            successMsg.style.cssText = 'background: var(--accent-success); color: white; padding: 10px; border-radius: 6px; margin-bottom: 10px; font-size: 0.9em;';
            successMsg.innerHTML = `已添加外设：${peripheralData.name} (${peripheralData.model})`;
            container.insertBefore(successMsg, container.firstChild);

            // 3秒后移除成功消息
            setTimeout(() => {
                if (successMsg.parentNode) {
                    successMsg.remove();
                }
            }, 3000);
        }

        function addPeripheralToDeviceForm(peripheralData) {
            const container = document.getElementById('device-peripherals-container');
            const placeholder = container.querySelector('p');
            if (placeholder) placeholder.remove();
            const configAttr = `data-config='${escapeHtml(JSON.stringify(peripheralData))}'`;

            const newPeripheralHtml = `
                <div class="peripheral-item" ${configAttr} style="display: grid; grid-template-columns: 1.5fr 1.5fr 2.5fr auto; gap: 10px; align-items: start; margin-bottom: 10px; background: var(--card-background); border: 1px solid var(--border-color); border-radius: 6px; padding: 10px;">
                    <div style="font-weight: bold; color: var(--text-primary);">${peripheralData.name}</div>
                    <div style="color: var(--text-secondary);">${peripheralData.model}</div>
                    <div class="pins-container" style="width: 100%; overflow: hidden;">
                        ${peripheralData.pins.map(pin => `
                            <div class="pin-item" style="display: flex; gap: 8px; margin-bottom: 5px; align-items: center; width: 100%;">
                                <span style="width: 45%; min-width: 50px; font-size: 0.85em; color: var(--text-secondary);">${pin.name}:</span>
                                <span style="width: 35%; min-width: 40px; font-size: 0.85em; font-weight: bold;">GPIO ${pin.number}</span>
                            </div>
                        `).join('')}
                    </div>
                    <div style="display: flex; gap: 5px;">
                        <button type="button" class="edit-peripheral-btn" onclick="editPeripheral(this)" title="编辑外设" style="background: var(--primary-color); color: white; border: none; border-radius: 4px; padding: 5px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.8'" onmouseout="this.style.opacity='1'">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                        </button>
                        <button type="button" class="remove-peripheral-btn" onclick="this.closest('.peripheral-item').remove()" title="删除外设" style="background: var(--accent-error); color: white; border: none; border-radius: 4px; padding: 5px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--accent-error-hover)'" onmouseout="this.style.background='var(--accent-error)'">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                        </button>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', newPeripheralHtml);

            // 显示成功消息
            const successMsg = document.createElement('div');
            successMsg.style.cssText = 'background: var(--accent-success); color: white; padding: 10px; border-radius: 6px; margin-bottom: 10px; font-size: 0.9em;';
            successMsg.innerHTML = `已添加外设：${peripheralData.name} (${peripheralData.model})`;
            container.insertBefore(successMsg, container.firstChild);

            // 3秒后移除成功消息
            setTimeout(() => {
                if (successMsg.parentNode) {
                    successMsg.remove();
                }
            }, 3000);
        }

        let editingPeripheralElement = null;

        function editPeripheral(button) {
            editingPeripheralElement = button.closest('.peripheral-item');
            const configString = editingPeripheralElement.dataset.config;

            if (!configString) {
                alert('找不到外设配置信息。请删除后重新添加。');
                return;
            }

            try {
                const config = JSON.parse(configString);

                // 提前进行数据验证和清理
                selectedPeripheralFunction = config.function;

                // 清理无效的功能类型值
                if (!selectedPeripheralFunction ||
                    selectedPeripheralFunction === 'undefined' ||
                    selectedPeripheralFunction === 'undefned' ||
                    selectedPeripheralFunction === 'null') {
                    selectedPeripheralFunction = 'UNKNOWN';
                }

                // 映射AI可能生成的功能类型到标准类型
                const functionTypeMapping = {
                    'distance-sensing': 'DISTANCE_MEASUREMENT',
                    'distance_sensing': 'DISTANCE_MEASUREMENT',
                    'light-sensing': 'LIGHT_SENSING',
                    'light_sensing': 'LIGHT_SENSING',
                    'temp-humidity-sensing': 'TEMP_HUMIDITY_SENSING',
                    'temp_humidity_sensing': 'TEMP_HUMIDITY_SENSING',
                    'motion-detection': 'MOTION_DETECTION',
                    'motion_detection': 'MOTION_DETECTION',
                    'buzzer': 'BUZZER',
                    'led-control': 'LED_CONTROL',
                    'led_control': 'LED_CONTROL',
                    'relay-switch': 'RELAY_SWITCH',
                    'relay_switch': 'RELAY_SWITCH'
                };

                if (functionTypeMapping[selectedPeripheralFunction]) {
                    selectedPeripheralFunction = functionTypeMapping[selectedPeripheralFunction];
                }

                const functionData = peripheralDatabase[selectedPeripheralFunction];
                if (!functionData) {
                    throw new Error(`无法识别的功能类型 '${selectedPeripheralFunction}'。该外设的定义可能已过时。请删除此外设并重新添加。`);
                }

                selectedImplementationType = config.implementationType;

                // 检查实现类型是否有效
                if (!selectedImplementationType || (selectedImplementationType !== 'SPECIFIC_MODEL' && selectedImplementationType !== 'GENERIC_COMPONENT')) {
                    throw new Error(`无效的实现类型 '${selectedImplementationType}'。`);
                }

                // 检查对应的组件数组是否存在
                const componentsArray = selectedImplementationType === 'SPECIFIC_MODEL'
                    ? functionData.specificModels
                    : functionData.genericComponents;

                if (!componentsArray || !Array.isArray(componentsArray)) {
                    throw new Error(`功能 '${selectedPeripheralFunction}' 缺少 ${selectedImplementationType === 'SPECIFIC_MODEL' ? '特定型号' : '通用组件'} 定义。`);
                }

                const components = componentsArray;
                const componentIndex = components.findIndex(c => c.name === config.model);
                if (componentIndex === -1) {
                    throw new Error(`在功能 '${selectedPeripheralFunction}' 下未找到组件 '${config.model}'。`);
                }

                selectedComponent = components[componentIndex];

                // 准备并打开对话框（跳过重置以保持我们设置的变量）
                openSmartPeripheralDialog(true);

                // 填充第一步和第二步的下拉框
                document.getElementById('peripheral-function').value = selectedPeripheralFunction;
                updateImplementationTypeOptions(selectedPeripheralFunction);
                document.getElementById('implementation-type').value = selectedImplementationType;

                // 生成第三步的整体结构
                generateDynamicConfig();

                // 使用微小的延迟来确保DOM更新
                setTimeout(() => {
                    // 设定第三步的组件下拉框
                    const componentSelect = document.getElementById('component-select');
                    componentSelect.value = componentIndex;

                    // 以编程方式触发change事件，来调用updatePinConfiguration生成输入框
                    // 这是最稳健的方式，确保了与用户手动操作的一致性
                    const event = new Event('change');
                    componentSelect.dispatchEvent(event);

                    // 再次延迟以确保引脚输入框已创建完毕
                    setTimeout(() => {
                        // 保存组件索引到 savedFormData
                        savedFormData.componentIndex = componentIndex;
                        savedFormData.pinValues = {};
                        savedFormData.configValues = {};

                        // 填充引脚值
                        if (config.pins && selectedComponent.pins) {
                            config.pins.forEach(savedPin => {
                                const pinTemplateIndex = selectedComponent.pins.findIndex(p => p.name === savedPin.name);
                                if (pinTemplateIndex !== -1) {
                                    const pinInput = document.getElementById(`pin-${pinTemplateIndex}`);
                                    if (pinInput) {
                                        pinInput.value = savedPin.number;
                                        // 同时保存到 savedFormData
                                        savedFormData.pinValues[pinTemplateIndex] = savedPin.number;
                                    }
                                }
                            });
                        }
                        // 填充附加配置值
                        if (config.config && selectedComponent.config) {
                             selectedComponent.config.forEach((configTemplate, index) => {
                                if (config.config.hasOwnProperty(configTemplate.name)) {
                                    const configInput = document.getElementById(`config-${index}`);
                                    if(configInput) {
                                        configInput.value = config.config[configTemplate.name];
                                        // 同时保存到 savedFormData
                                        savedFormData.configValues[index] = config.config[configTemplate.name];
                                    }
                                }
                            });
                        }
                    }, 50);
                }, 100);

                // 最后，跳转到第三步
                showSmartPeripheralStep(3);

            } catch (e) {
                console.error('加载外设配置失败:', e);
                alert('加载外设配置失败，请尝试删除后重新添加。\n错误: ' + e.message);
                editingPeripheralElement = null;
                document.getElementById('smart-peripheral-dialog').close();
            }
        }

        function replacePeripheralElement(peripheralData) {
            if (!editingPeripheralElement) return;

            // 更新data-config属性
            editingPeripheralElement.dataset.config = JSON.stringify(peripheralData);

            // 构建引脚配置字符串
            const pinsHtml = (peripheralData.pins && Array.isArray(peripheralData.pins)) ?
                peripheralData.pins.map(pin =>
                    `<div class="pin-item" style="display: flex; gap: 8px; margin-bottom: 5px; align-items: center; width: 100%;">
                        <span style="width: 45%; min-width: 50px; font-size: 0.85em; color: var(--text-secondary);">${pin && pin.name ? pin.name : '未知引脚'}:</span>
                        <span style="width: 35%; min-width: 40px; font-size: 0.85em; font-weight: bold;">GPIO ${pin && pin.number ? pin.number : '?'}</span>
                    </div>`
                ).join('') : '<div style="color: var(--text-secondary); font-size: 0.85em;">无引脚配置</div>';

            // 更新外设元素内容
            editingPeripheralElement.innerHTML = `
                <div style="font-weight: bold; color: var(--text-primary);">${peripheralData.name}</div>
                <div style="color: var(--text-secondary);">${peripheralData.model}</div>
                <div class="pins-container" style="width: 100%; overflow: hidden;">
                    ${pinsHtml}
                </div>
                <div style="display: flex; gap: 5px;">
                    <button type="button" class="edit-peripheral-btn" onclick="editPeripheral(this)" title="编辑外设" style="background: var(--primary-color); color: white; border: none; border-radius: 4px; padding: 5px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.8'" onmouseout="this.style.opacity='1'">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                    </button>
                    <button type="button" class="remove-peripheral-btn" onclick="this.closest('.peripheral-item').remove()" title="删除外设" style="background: var(--accent-error); color: white; border: none; border-radius: 4px; padding: 5px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--accent-error-hover)'" onmouseout="this.style.background='var(--accent-error)'">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
            `;

            // 显示更新成功消息
            const successMsg = document.createElement('div');
            successMsg.style.cssText = 'background: var(--accent-success); color: white; padding: 10px; border-radius: 6px; margin-bottom: 10px; font-size: 0.9em; position: absolute; top: -40px; left: 0; right: 0; z-index: 1000;';
            successMsg.innerHTML = `已更新外设：${peripheralData.name} (${peripheralData.model})`;

            editingPeripheralElement.style.position = 'relative';
            editingPeripheralElement.appendChild(successMsg);

            // 3秒后移除成功消息
            setTimeout(() => {
                if (successMsg.parentNode) {
                    successMsg.remove();
                }
            }, 3000);
        }

        // togglePasswordVisibility 和 toggleForms 现在已经在全局作用域中定义
        window.openEditDeviceDialog = openEditDeviceDialog;
        window.deleteDevice = deleteDevice;

        window.openSmartPeripheralDialog = openSmartPeripheralDialog;
        window.openSmartPeripheralDialogForTask = openSmartPeripheralDialogForTask;
        window.smartPeripheralNextStep = smartPeripheralNextStep;
        window.smartPeripheralPrevStep = smartPeripheralPrevStep;
        window.updatePinConfiguration = updatePinConfiguration;
        window.submitSmartPeripheral = submitSmartPeripheral;
        window.editPeripheral = editPeripheral;
        window.replacePeripheralElement = replacePeripheralElement;
        window.refreshCommunicationPlan = refreshCommunicationPlan;
        window.closeProblemsPanel = closeProblemsPanel;
        window.toggleProblemsPanel = toggleProblemsPanel;
        window.filterProblems = filterProblems;
        window.postDeploymentAction = postDeploymentAction;
        window.performWorkflowAction = performWorkflowAction; // 确保这个函数也暴露出去


        const rawTextInput = document.getElementById('raw-text-input');
        const sendPromptBtn = document.getElementById('send-prompt-btn');
        const expandInputBtn = document.getElementById('expand-input-btn');
        const promptContainer = document.getElementById('initial-prompt-container');

        rawTextInput.addEventListener('input', () => {
            // 自动增高
            rawTextInput.style.height = 'auto';
            rawTextInput.style.height = rawTextInput.scrollHeight + 'px';
            // 启用/禁用发送按钮
            sendPromptBtn.disabled = !rawTextInput.value.trim();
        });

        rawTextInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (!sendPromptBtn.disabled) {
                    sendPromptBtn.click();
                }
            }
        });

        sendPromptBtn.addEventListener('click', handleInitialSubmit);
        expandInputBtn.addEventListener('click', () => {
            const workflowView = document.getElementById('view-ai-workflow');
            const inputWrapper = document.getElementById('prompt-input-wrapper');

            if (workflowView.classList.contains('fullscreen-active')) {
                // 缩小时：记录当前内容，然后恢复到原始高度
                const currentContent = document.getElementById('raw-text-input').value;

                // 移除全屏类
                workflowView.classList.remove('fullscreen-active');

                // 如果没有内容或只有空白，强制设置为自动高度
                if (!currentContent.trim()) {
                    setTimeout(() => {
                        inputWrapper.style.height = 'auto';
                        // 确保文本域也回到原始状态
                        const textInput = document.getElementById('raw-text-input');
                        textInput.style.height = 'auto';
                    }, 100);
                }
            } else {
                // 展开时：记录原始高度，然后展开
                const originalHeight = inputWrapper.offsetHeight;
                inputWrapper.setAttribute('data-original-height', originalHeight + 'px');

                // 清除任何内联样式，让CSS接管
                inputWrapper.style.height = '';

                workflowView.classList.add('fullscreen-active');
            }
        });
        document.getElementById('login-button').addEventListener('click', login);
        document.getElementById('register-button').addEventListener('click', register);
        document.getElementById('logout-button').addEventListener('click', () => {
            localStorage.clear();
            location.reload();
        });
        document.getElementById('add-device-btn').addEventListener('click', () => {
            document.getElementById('device-form').reset();
            document.getElementById('device-dialog-title').textContent = '注册新设备';
            document.getElementById('device-form-submit-btn').textContent = '注册';
            document.getElementById('device-internal-id').value = '';
            renderDevicePeripherals([]);
            document.getElementById('device-dialog').showModal();
        });
        document.getElementById('device-form').addEventListener('submit', handleDeviceFormSubmit);
        document.getElementById('save-file-btn').addEventListener('click', saveFileContent);
        document.getElementById('open-local-folder-btn').addEventListener('click', openLocalFolder);
        document.getElementById('flash-firmware-btn').addEventListener('click', flashFirmware);

        // --- 【核心修改】项目加载和模板删除逻辑 ---
        document.getElementById('load-cloud-project-btn').addEventListener('click', async () => {
            const projectLoaderDialog = document.getElementById('project-loader-dialog');
            const projectList = document.getElementById('project-list');
            projectList.innerHTML = '<li>加载中...</li>';
            projectLoaderDialog.showModal();
            try {
                const [history, savedProjects] = await Promise.all([
                    fetchApi('/workflows/history').catch(() => []),
                    fetchApi('/projects').catch(() => [])
                ]);

                projectList.innerHTML = '';

                if (history.length > 0) {
                    projectList.innerHTML += '<li style="color: var(--text-secondary); pointer-events: none; font-weight: bold;">构建历史 (可查看文件/删除)</li>';
                    history.forEach(wf => {
                        const li = document.createElement('li');
                        li.innerHTML = `
                            <span class="project-name" title="点击加载文件">${escapeHtml(wf.project_name)}</span>
                            <div>
                               <span class="status-badge ${wf.status.toLowerCase()}">${escapeHtml(wf.status)}</span>
                               <button class="delete-project-btn" title="删除此项目及文件">
                                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/></svg>
                               </button>
                            </div>`;

                        li.querySelector('.project-name').onclick = () => {
                            document.getElementById('btn-activity-files').click();
                            startPolling(wf.workflow_id);
                            loadAndRenderFileTree(wf.workflow_id);
                            projectLoaderDialog.close();
                        };

                        li.querySelector('.delete-project-btn').onclick = async (e) => {
                            e.stopPropagation();
                            try {
                                const deleteBtn = e.currentTarget;
                                deleteBtn.innerHTML = '...';
                                deleteBtn.disabled = true;
                                await fetchApi(`/workflows/${wf.workflow_id}`, 'DELETE', null, 200);
                                li.remove();
                            } catch (error) {
                                alert(`删除失败: ${error.message}`);
                            }
                        };
                        projectList.appendChild(li);
                    });
                }

                if (savedProjects.length > 0) {
                    projectList.innerHTML += '<li style="color: var(--text-secondary); pointer-events: none; font-weight: bold; margin-top: 15px;">项目模板 (可使用/删除)</li>';
                    savedProjects.forEach(p => {
                        const li = document.createElement('li');
                        li.innerHTML = `
                            <span class="project-name" title="点击使用此模板开始新构建">${escapeHtml(p.name)}</span>
                            <button class="delete-project-btn" title="删除此模板">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/></svg>
                            </button>`;

                        li.querySelector('.project-name').onclick = async () => {
                            try {
                                const projectData = await fetchApi(`/projects/${p.id}`);
                                populateConfirmationDialog(projectData.config_json);
                                projectLoaderDialog.close();
                            } catch (error) {
                                alert(`加载项目模板失败: ${error.message}`);
                            }
                        };

                        li.querySelector('.delete-project-btn').onclick = async (e) => {
                            e.stopPropagation();
                            try {
                                const deleteBtn = e.currentTarget;
                                deleteBtn.innerHTML = '...';
                                deleteBtn.disabled = true;
                                await fetchApi(`/projects/${p.id}`, 'DELETE', null, 200);
                                li.remove();
                            } catch (error) {
                                alert(`删除模板失败: ${error.message}`);
                            }
                        };
                        projectList.appendChild(li);
                    });
                }

                if (projectList.innerHTML === '') {
                    projectList.innerHTML = '<li>没有找到任何项目或历史记录。</li>';
                }
            } catch (error) {
                projectList.innerHTML = `<li>加载失败: ${error.message}</li>`;
            }
        });

        // 检查 JSZip 是否加载
        if (typeof JSZip === 'undefined') {
            console.warn('JSZip not loaded yet, will retry when needed');
        } else {
            console.log('JSZip loaded successfully');
        }

        // Check for stored token on load
        const storedToken = localStorage.getItem('accessToken');
        const storedUsername = localStorage.getItem('username');
        if (storedToken && storedUsername) {
            accessToken = storedToken;
            showIde(storedUsername);
        }
    });

    // ===================================================================
    // 全局变量和函数 (必须在全局作用域中以便onclick调用)
    // ===================================================================

    // 全局变量
    let accessToken = null;
    const API_BASE_URL = '/api/v1';

    // 全局API请求函数
    async function fetchApi(endpoint, method = 'GET', body = null, expectedStatus) {
        const options = { method, headers: { 'Authorization': `Bearer ${accessToken}` } };
        if (body) {
            options.headers['Content-Type'] = 'application/json';
            options.body = JSON.stringify(body);
        }
        const response = await fetch(API_BASE_URL + endpoint, options);
        const responseText = await response.text();
        const isSuccess = expectedStatus ? response.status === expectedStatus : response.ok;
        if (!isSuccess) {
            let errorMsg = `请求失败，状态码: ${response.status}`;
            let errorData = null;
            try {
                errorData = JSON.parse(responseText);
                errorMsg = errorData.error || errorMsg;
            } catch (e) {}

            const error = new Error(errorMsg);
            error.response = response;
            error.responseData = errorData;
            throw error;
        }
        try { return JSON.parse(responseText); } catch (e) { return {}; }
    }

    // 存储MQTT事件源连接
    const mqttEventSources = new Map();

    async function initializeMqttStatus(deviceId) {
        try {
            // 获取设备MQTT状态
            const status = await fetchApi(`/mqtt/devices/${deviceId}/mqtt/status`);
            console.log(`设备 ${deviceId} MQTT状态:`, status); // 添加调试日志
            updateMqttStatusUI(deviceId, status);

            // 如果监控已启用，开始接收日志流
            if (status.monitoring_enabled && status.is_connected) {
                startMqttLogStream(deviceId);
            } else if (status.monitoring_enabled && !status.is_connected) {
                console.warn(`设备 ${deviceId} 监控已启用但未连接，可能需要等待连接建立`);
                // 延迟重试检查连接状态
                setTimeout(() => initializeMqttStatus(deviceId), 3000);
            }
        } catch (error) {
            console.error(`初始化设备 ${deviceId} MQTT状态失败:`, error);
        }
    }

    function updateMqttStatusUI(deviceId, status) {
        const statusIndicator = document.getElementById(`mqtt-status-${deviceId}`);
        const statusText = document.getElementById(`mqtt-status-text-${deviceId}`);
        const toggleBtn = document.getElementById(`mqtt-toggle-${deviceId}`);

        if (!statusIndicator || !statusText || !toggleBtn) return;

        if (status.monitoring_enabled) {
            if (status.is_connected) {
                statusIndicator.className = 'mqtt-status-indicator connected';
                statusText.textContent = '已连接';
                toggleBtn.textContent = '停止监控';
            } else {
                statusIndicator.className = 'mqtt-status-indicator disconnected';
                statusText.textContent = '连接中...';
                toggleBtn.textContent = '停止监控';
            }
        } else {
            statusIndicator.className = 'mqtt-status-indicator disabled';
            statusText.textContent = '未启用';
            toggleBtn.textContent = '启用监控';
        }
    }

    async function toggleMqttMonitoring(deviceId) {
        try {
            const status = await fetchApi(`/mqtt/devices/${deviceId}/mqtt/status`);
            const newEnabled = !status.monitoring_enabled;

            await fetchApi(`/mqtt/devices/${deviceId}/mqtt/config`, 'PUT', {
                mqtt_monitoring_enabled: newEnabled
            });

            if (newEnabled) {
                startMqttLogStream(deviceId);
            } else {
                stopMqttLogStream(deviceId);
            }

            // 更新状态
            await initializeMqttStatus(deviceId);
        } catch (error) {
            alert(`切换MQTT监控失败: ${error.message}`);
        }
    }

    function startMqttLogStream(deviceId) {
        // 如果已有连接，先关闭
        stopMqttLogStream(deviceId);

        // EventSource不支持自定义headers，所以通过URL参数传递token
        const streamUrl = `/api/v1/mqtt/devices/${deviceId}/logs/stream?token=${encodeURIComponent(accessToken)}`;
        const eventSource = new EventSource(streamUrl);

        eventSource.onmessage = function(event) {
            try {
                const logData = JSON.parse(event.data);
                if (logData.error) {
                    console.error('MQTT日志流错误:', logData.error);
                    return;
                }
                addMqttLogEntry(deviceId, logData);
            } catch (error) {
                console.error('解析MQTT日志数据失败:', error);
            }
        };

        eventSource.onerror = function(event) {
            console.error('MQTT日志流连接错误:', event);
            // 可以在这里实现重连逻辑
        };

        mqttEventSources.set(deviceId, eventSource);
    }

    function stopMqttLogStream(deviceId) {
        const eventSource = mqttEventSources.get(deviceId);
        if (eventSource) {
            eventSource.close();
            mqttEventSources.delete(deviceId);
        }
    }

    function addMqttLogEntry(deviceId, logData) {
        const container = document.getElementById(`mqtt-log-container-${deviceId}`);
        if (!container) return;

        // 如果是第一条日志，清空提示文本
        if (container.children.length === 1 && container.children[0].style.textAlign === 'center') {
            container.innerHTML = '';
        }

        const time = new Date(logData.timestamp).toLocaleTimeString();
        const direction = logData.direction === 'incoming' ? '📥' : '📤';

        const logEntry = document.createElement('div');
        logEntry.className = `mqtt-log-entry ${logData.direction}`;

        // 格式化payload，如果是JSON则美化显示
        let formattedPayload = logData.payload;
        try {
            const parsed = JSON.parse(logData.payload);
            formattedPayload = JSON.stringify(parsed, null, 2);
        } catch (e) {
            // 不是JSON，保持原样
        }

        logEntry.innerHTML = `
            <div class="mqtt-log-header">
                <span class="mqtt-log-time">${time}</span>
                <span class="mqtt-log-direction ${logData.direction}">${direction}</span>
                <span class="mqtt-log-topic">${logData.topic}</span>
            </div>
            <div class="mqtt-log-payload">${formattedPayload}</div>
        `;

        container.appendChild(logEntry);

        // 保持最多100条日志
        while (container.children.length > 100) {
            container.removeChild(container.firstChild);
        }

        // 自动滚动到底部
        container.scrollTop = container.scrollHeight;
    }

    async function clearMqttLogs(deviceId) {
        if (!confirm('确定要清空该设备的所有MQTT日志吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetchApi(`/mqtt/logs/${deviceId}`, 'DELETE');

            // 清空前端显示
            const container = document.getElementById(`mqtt-log-container-${deviceId}`);
            if (container) {
                container.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">日志已清空</div>';
            }

            console.log(`已清空设备 ${deviceId} 的MQTT日志:`, response);
        } catch (error) {
            console.error('清空MQTT日志失败:', error);
            alert(`清空日志失败: ${error.message}`);
        }
    }

    async function debugMqttStatus(deviceId) {
        console.log('=== MQTT调试信息 ===');
        try {
            // 获取状态
            const status = await fetchApi(`/mqtt/devices/${deviceId}/mqtt/status`);
            console.log('MQTT状态:', status);

            // 获取配置
            const config = await fetchApi(`/mqtt/devices/${deviceId}/mqtt/config`);
            console.log('MQTT配置:', config);

            // 测试连接
            const testResult = await fetchApi(`/mqtt/devices/${deviceId}/mqtt/test`, 'POST', {
                mqtt_broker_host: config.mqtt_broker_host,
                mqtt_broker_port: config.mqtt_broker_port,
                mqtt_username: config.mqtt_username,
                mqtt_password: config.mqtt_password
            });
            console.log('连接测试结果:', testResult);

            alert(`调试信息已输出到控制台\n监控状态: ${status.monitoring_enabled ? '已启用' : '未启用'}\n连接状态: ${status.is_connected ? '已连接' : '未连接'}`);
        } catch (error) {
            console.error('调试失败:', error);
            alert('调试失败: ' + error.message);
        }
    }

    async function openMqttConfigDialog(deviceId) {
        try {
            // 获取当前MQTT配置
            const config = await fetchApi(`/mqtt/devices/${deviceId}/mqtt/config`);

            // 填充表单
            document.getElementById('mqtt-device-id').value = deviceId;
            document.getElementById('mqtt-broker-host').value = config.mqtt_broker_host || '';
            document.getElementById('mqtt-broker-port').value = config.mqtt_broker_port || 1883;
            document.getElementById('mqtt-username').value = config.mqtt_username || '';
            document.getElementById('mqtt-password').value = ''; // 出于安全考虑不显示密码
            document.getElementById('mqtt-client-id').value = config.mqtt_client_id || '';
            setMqttTopics(config.mqtt_subscribe_topics || []);
            document.getElementById('mqtt-monitoring-enabled').checked = config.mqtt_monitoring_enabled || false;

            // 清空错误信息
            const errorDiv = document.getElementById('mqtt-config-error');
            errorDiv.innerHTML = '';
            errorDiv.style.color = 'var(--accent-error)';

            // 添加输入验证
            const hostInput = document.getElementById('mqtt-broker-host');
            // 移除之前的事件监听器（如果有的话）
            hostInput.removeEventListener('blur', hostInput._mqttValidation);

            // 添加新的验证函数
            hostInput._mqttValidation = function() {
                const originalValue = this.value;
                const cleanedValue = cleanMqttHost(originalValue);
                if (originalValue !== cleanedValue && cleanedValue) {
                    this.value = cleanedValue;
                    errorDiv.style.color = 'var(--accent-primary)';
                    errorDiv.innerHTML = `💡 已自动清理主机名: ${originalValue} → ${cleanedValue}`;
                    setTimeout(() => {
                        if (errorDiv.innerHTML.includes('已自动清理')) {
                            errorDiv.innerHTML = '';
                        }
                    }, 3000);
                }
            };
            hostInput.addEventListener('blur', hostInput._mqttValidation);

            // 显示对话框
            document.getElementById('mqtt-config-dialog').showModal();
        } catch (error) {
            alert(`获取MQTT配置失败: ${error.message}`);
        }
    }

    function setMqttPreset(host) {
        document.getElementById('mqtt-broker-host').value = host;

        // 根据不同的预设设置不同的默认配置
        const presets = {
            'localhost': { port: 1883, username: '', password: '', topics: ['test/topic'] },
            '127.0.0.1': { port: 1883, username: '', password: '', topics: ['test/topic'] },
            '*************': { port: 1883, username: '', password: '', topics: ['test/topic'] },
            'test.mosquitto.org': { port: 1883, username: '', password: '', topics: ['$SYS/broker/version'] }
        };

        const preset = presets[host];
        if (preset) {
            document.getElementById('mqtt-broker-port').value = preset.port;
            document.getElementById('mqtt-username').value = preset.username;
            document.getElementById('mqtt-password').value = preset.password;
            setMqttTopics(preset.topics);
        }
    }

    // MQTT主题管理函数
    function addMqttTopicInput(value = '') {
        const container = document.getElementById('mqtt-topics-container');
        const row = document.createElement('div');
        row.className = 'mqtt-topic-input-row';

        const inputId = 'mqtt-topic-' + Date.now();
        row.innerHTML = `
            <div class="mqtt-input-with-icon">
                <span class="mqtt-input-icon">📡</span>
                <input type="text" id="${inputId}" placeholder="例如: sensor/+/temperature" value="${value}">
            </div>
            <button type="button" class="mqtt-topic-remove-btn" onclick="removeMqttTopicInput(this)" title="删除主题">
                🗑️
            </button>
        `;

        container.appendChild(row);

        // 如果是第一个输入框，聚焦
        if (container.children.length === 1) {
            row.querySelector('input').focus();
        }
    }

    function removeMqttTopicInput(button) {
        const row = button.parentElement;
        row.remove();

        // 如果没有输入框了，至少保留一个
        const container = document.getElementById('mqtt-topics-container');
        if (container.children.length === 0) {
            addMqttTopicInput();
        }
    }

    function addCommonMqttTopics() {
        const commonTopics = [
            '$SYS/broker/version',
            'sensor/+/temperature',
            'device/+/status',
            'data/+/report',
            'test/topic'
        ];

        commonTopics.forEach(topic => {
            addMqttTopicInput(topic);
        });
    }

    function getMqttTopics() {
        const container = document.getElementById('mqtt-topics-container');
        const topics = [];

        container.querySelectorAll('input[type="text"]').forEach(input => {
            const value = input.value.trim();
            if (value) {
                topics.push(value);
            }
        });

        return topics;
    }

    function setMqttTopics(topics) {
        const container = document.getElementById('mqtt-topics-container');
        container.innerHTML = '';

        if (topics && topics.length > 0) {
            topics.forEach(topic => {
                addMqttTopicInput(topic);
            });
        } else {
            // 至少添加一个空的输入框
            addMqttTopicInput();
        }
    }

    function cleanMqttHost(host) {
        // 移除协议前缀
        host = host.replace(/^(mqtt:\/\/|mqtts:\/\/|tcp:\/\/|ssl:\/\/)/i, '');
        // 移除路径部分
        host = host.split('/')[0];
        return host.trim();
    }

    async function testMqttConnection() {
        const deviceId = document.getElementById('mqtt-device-id').value;
        const testBtn = document.getElementById('mqtt-test-btn');
        const errorDiv = document.getElementById('mqtt-config-error');

        // 收集配置数据并清理主机名
        let brokerHost = document.getElementById('mqtt-broker-host').value;
        brokerHost = cleanMqttHost(brokerHost);

        // 如果主机名被清理了，更新输入框
        if (brokerHost !== document.getElementById('mqtt-broker-host').value) {
            document.getElementById('mqtt-broker-host').value = brokerHost;
        }

        const config = {
            mqtt_broker_host: brokerHost,
            mqtt_broker_port: parseInt(document.getElementById('mqtt-broker-port').value),
            mqtt_username: document.getElementById('mqtt-username').value || null,
            mqtt_password: document.getElementById('mqtt-password').value || null,
            mqtt_client_id: document.getElementById('mqtt-client-id').value || null
        };

        // 验证必填字段
        if (!config.mqtt_broker_host) {
            errorDiv.textContent = '请填写Broker地址';
            return;
        }

        // 更新按钮状态
        testBtn.disabled = true;
        testBtn.textContent = '🔄 测试中...';
        errorDiv.textContent = '';

        try {
            const result = await fetchApi(`/mqtt/devices/${deviceId}/mqtt/test`, 'POST', config);

            if (result.success) {
                errorDiv.style.color = 'var(--accent-success)';
                errorDiv.innerHTML = '✅ ' + result.message;
            } else {
                errorDiv.style.color = 'var(--accent-error)';
                errorDiv.innerHTML = '❌ ' + result.error.replace(/\n/g, '<br>');
            }
        } catch (error) {
            errorDiv.style.color = 'var(--accent-error)';
            errorDiv.innerHTML = '❌ 测试失败: ' + error.message.replace(/\n/g, '<br>');
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = '🔍 测试连接';
        }
    }

    async function handleMqttConfigSubmit(event) {
        event.preventDefault();

        const deviceId = document.getElementById('mqtt-device-id').value;
        const topics = getMqttTopics();

        // 清理主机名
        let brokerHost = document.getElementById('mqtt-broker-host').value;
        brokerHost = cleanMqttHost(brokerHost);

        // 如果主机名被清理了，更新输入框
        if (brokerHost !== document.getElementById('mqtt-broker-host').value) {
            document.getElementById('mqtt-broker-host').value = brokerHost;
        }

        const config = {
            mqtt_broker_host: brokerHost,
            mqtt_broker_port: parseInt(document.getElementById('mqtt-broker-port').value),
            mqtt_username: document.getElementById('mqtt-username').value || null,
            mqtt_password: document.getElementById('mqtt-password').value || null,
            mqtt_client_id: document.getElementById('mqtt-client-id').value || null,
            mqtt_subscribe_topics: topics,
            mqtt_monitoring_enabled: document.getElementById('mqtt-monitoring-enabled').checked
        };

        try {
            await fetchApi(`/mqtt/devices/${deviceId}/mqtt/config`, 'PUT', config);
            document.getElementById('mqtt-config-dialog').close();

            // 重新初始化MQTT状态
            await initializeMqttStatus(deviceId);

            alert('MQTT配置保存成功！');
        } catch (error) {
            document.getElementById('mqtt-config-error').textContent = error.message;
        }
    }

}); // 闭合 DOMContentLoaded 事件监听器





    </script>
</body>
</html>