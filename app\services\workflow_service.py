# app/services/workflow_service.py
# -*- coding: utf-8 -*-

import threading
import uuid
import time
import re
import random
from pathlib import Path
from langgraph.graph import END
from flask import current_app
import json
import traceback
from sqlalchemy import text  # 核心导入: 用于执行原生SQL以实现原子更新

# 核心修改: 导入数据库模型和实例
from app.langgraph_def.graph_builder import build_graph
from app.langgraph_def.agent_state import AgentState, WorkflowStep
from app.models import Device, WorkflowState, User
from app import db

# 核心重构: 移除内存字典，改为延迟加载图
_GRAPH = None
_GRAPH_LOCK = threading.Lock()

# 【兼容性修正】重新添加 WORKFLOWS 和 WORKFLOW_LOCK 以解决旧模块的 ImportError
# 注意：这些变量不再用于主工作流状态管理，仅为兼容 log_stream_routes.py。
WORKFLOWS = {}
WORKFLOW_LOCK = threading.Lock()

# 为节点ID提供一个友好的UI显示名称映射
NODE_FRIENDLY_NAMES = {
    "master_router": "需求分析",
    "device_dispatcher": "设备规划",
    "module_architect": "架构设计",
    "module_dispatcher": "模块分配",
    "api_designer": "接口设计",
    "developer": "代码开发",
    "integrator": "代码整合",
    "test_plan_designer": "测试设计",
    "deployment_and_verification": "环境准备",
    "compile_node": "代码编译",
    "pre_deployment_pause": "等待部署",
    "usb_upload_node": "USB烧录",
    "ota_deployment_node": "无线部署",
    "deploy_and_verify_node": "部署验证",
    "dp_extractor": "功能提取",
    "resume_router": "流程恢复"
}

# 智能错误分析模式库
ERROR_PATTERNS = {
    "undeclared_identifier": {
        "pattern": r"'(\w+)' was not declared in this scope",
        "analysis": "变量或函数未声明",
        "ai_thoughts": [
            "啊，我明白了！我忘记声明 {identifier} 了",
            "看起来 {identifier} 没有在作用域内声明",
            "我需要检查 {identifier} 的声明位置"
        ],
        "fix_hints": ["检查头文件包含", "确认变量声明", "检查作用域"]
    },
    "missing_header": {
        "pattern": r"fatal error: ([^:]+): No such file or directory",
        "analysis": "缺少头文件",
        "ai_thoughts": [
            "我需要添加 {header} 头文件的包含",
            "缺少了 {header} 这个重要的头文件",
            "让我添加对 {header} 的引用"
        ],
        "fix_hints": ["添加#include语句", "检查文件路径", "确认库依赖"]
    },
    "syntax_error": {
        "pattern": r"expected '(.+)' before '(.+)'",
        "analysis": "语法错误",
        "ai_thoughts": [
            "语法有问题，我需要仔细检查代码结构",
            "看起来是语法错误，让我修正一下",
            "代码结构不对，我来调整语法"
        ],
        "fix_hints": ["检查括号匹配", "确认语句结构", "检查分号"]
    },
    "type_error": {
        "pattern": r"cannot convert '(.+)' to '(.+)'",
        "analysis": "类型转换错误",
        "ai_thoughts": [
            "类型不匹配，我需要进行正确的类型转换",
            "数据类型有问题，让我修正一下",
            "需要调整变量的数据类型"
        ],
        "fix_hints": ["检查变量类型", "添加类型转换", "确认函数参数"]
    }
}


def get_graph():
    """
    延迟初始化函数，确保昂贵的图编译操作只在应用生命周期中执行一次。
    """
    global _GRAPH
    if _GRAPH is None:
        with _GRAPH_LOCK:
            if _GRAPH is None:
                print("--- [System Startup] Compiling workflow graph for the first time... ---")
                _GRAPH = build_graph()
                print("--- [System Startup] Graph compilation complete. ---")
    return _GRAPH


def find_step(steps: list, step_id: str) -> dict | None:
    """在步骤列表中通过ID查找特定步骤。"""
    return next((s for s in steps if s['id'] == step_id), None)


# --- 核心重构: 数据库状态管理辅助函数 ---

def _load_state(workflow_id: str) -> dict | None:
    """从数据库加载并反序列化工作流状态。"""
    record = WorkflowState.query.get(workflow_id)
    if record:
        return json.loads(record.state_json)
    return None


def _save_state(workflow_id: str, state_data: dict):
    """序列化并保存工作流状态到数据库。"""
    record = WorkflowState.query.get(workflow_id)
    state_json_str = json.dumps(state_data)
    if record:
        record.state_json = state_json_str
    else:
        record = WorkflowState(workflow_id=workflow_id, state_json=state_json_str)
        db.session.add(record)
    db.session.commit()


def _delete_state(workflow_id: str):
    """从数据库删除工作流状态。"""
    record = WorkflowState.query.get(workflow_id)
    if record:
        db.session.delete(record)
        db.session.commit()


def _log(workflow_id: str, message: str):
    """
    【核心新增】将日志消息附加到数据库记录中，并同时在终端打印。
    """
    try:
        timestamp = time.strftime('%H:%M:%S', time.localtime())
        log_line_for_print = f"[{timestamp}] {message}"
        log_line_for_db = f"{log_line_for_print}\n"

        # 1. 立即在终端打印，用于实时调试
        print(log_line_for_print)

        # 2. 使用 SQLAlchemy Core API 以确保在多线程环境下的原子性追加操作
        stmt = (
            text("UPDATE workflow_states SET logs = logs || :log_line WHERE workflow_id = :wid")
        )
        result = db.session.execute(stmt, {"log_line": log_line_for_db, "wid": workflow_id})
        db.session.commit()
        print(f"[DEBUG] 日志写入数据库，影响行数: {result.rowcount}")  # 调试信息
    except Exception as e:
        print(f"FATAL ERROR: Failed to write log to database for {workflow_id}: {e}")
        db.session.rollback()


def _log_with_personality(workflow_id: str, message: str, log_type: str = "info", delay: float = 0):
    """
    带有AI人格的智能日志输出
    """
    emoji_map = {
        "thinking": "💭",
        "planning": "🏗️",
        "coding": "📝",
        "compiling": "🔧",
        "error": "❌",
        "analyzing": "🔍",
        "fixing": "🛠️",
        "success": "✅",
        "warning": "⚠️",
        "info": "ℹ️"
    }

    emoji = emoji_map.get(log_type, "ℹ️")
    formatted_message = f"{emoji} {message}"

    # 添加思考延迟，模拟真实的思考过程
    if delay > 0:
        time.sleep(delay)

    _log(workflow_id, formatted_message)


def analyze_compilation_error(error_log: str) -> dict:
    """
    智能分析编译错误，返回人性化的分析结果
    """
    analysis = {
        "error_type": "unknown",
        "file_location": "unknown",
        "line_number": None,
        "error_message": "",
        "ai_thought": "让我看看哪里出了问题...",
        "fix_strategy": "general_retry",
        "identified_issue": None
    }

    # 提取文件和行号信息
    location_patterns = [
        r'([^/\\:\s]+\.(cpp|h|ino|c)):(\d+):(?:\d+:)?\s*(?:fatal\s+)?error:\s*(.+)',
        r'In file included from.*?([^/\\:\s]+\.(cpp|h|ino|c)):(\d+)',
        r'([^/\\:\s]+\.(cpp|h|ino|c)): In function.*?:(\d+):(?:\d+:)?\s*error:\s*(.+)'
    ]

    for pattern in location_patterns:
        location_match = re.search(pattern, error_log, re.MULTILINE)
        if location_match:
            analysis["file_location"] = location_match.group(1)
            analysis["line_number"] = location_match.group(3)
            if len(location_match.groups()) >= 4:
                analysis["error_message"] = location_match.group(4).strip()
            break

    # 匹配错误模式并生成智能回复
    for error_type, pattern_info in ERROR_PATTERNS.items():
        match = re.search(pattern_info["pattern"], error_log, re.MULTILINE | re.IGNORECASE)
        if match:
            analysis["error_type"] = error_type
            analysis["identified_issue"] = match.group(1) if match.groups() else None

            # 选择合适的AI思考回复
            thought_template = random.choice(pattern_info["ai_thoughts"])
            if analysis["identified_issue"]:
                if error_type == "undeclared_identifier":
                    analysis["ai_thought"] = thought_template.format(identifier=analysis["identified_issue"])
                elif error_type == "missing_header":
                    analysis["ai_thought"] = thought_template.format(header=analysis["identified_issue"])
                else:
                    analysis["ai_thought"] = thought_template
            else:
                analysis["ai_thought"] = thought_template

            analysis["fix_strategy"] = error_type
            break

    return analysis


# --- 重构后的服务函数 ---

def _run_graph_in_thread(app, workflow_id: str, initial_state: AgentState):
    """
    【V3.3 架构重构版】在后台线程中执行LangGraph工作流。
    此版本拥有一个更健壮、更符合逻辑的状态更新循环。
    """
    with app.app_context():
        try:
            graph_to_run = get_graph()
            config = {"recursion_limit": 100, "configurable": {"thread_id": workflow_id}}

            # 在图开始前，我们并不知道第一个节点是什么，所以我们让循环来处理第一个节点的启动状态

            for step in graph_to_run.stream(initial_state, config):
                # --- 1. 健壮地解析 stream 的每一步 ---
                # stream 的产物是一个字典，key是节点名，value是该节点的返回状态更新
                if not isinstance(step, dict) or not step:
                    continue

                try:
                    # 使用 list(step.items())[0] 来安全地获取第一个键值对
                    current_node_id, state_update_from_node = list(step.items())[0]
                except IndexError:
                    continue  # 如果是空字典，则跳过

                # --- 2. 核心保护：防止 'NoneType' 错误 ---
                # 如果节点的返回值为 None，我们将其视为空字典，避免后续操作崩溃。
                if state_update_from_node is None:
                    state_update_from_node = {}

                # --- 3. 加载最新的工作流数据 ---
                workflow_data = _load_state(workflow_id)
                if not workflow_data:
                    print(f"工作流 {workflow_id} 状态从数据库中消失，线程中止。")
                    break

                # --- 4. 实现更清晰的状态更新逻辑 ---
                latest_state = workflow_data['latest_state']
                steps_list = latest_state['workflow_steps']
                latest_state.update(state_update_from_node)

                # --- [最终修正] 在设备分发节点完成后，打印设备切换的分隔符 ---
                if current_node_id == "device_dispatcher":
                    new_device_task = latest_state.get('current_device_task')
                    if new_device_task:
                        device_role = new_device_task.get('device_role', 'Unknown Device')
                        _log_with_personality(workflow_id, f"现在开始为{device_role}设计完整的解决方案...", "planning", delay=1.0)
                # --- 修正结束 ---

                # 找到刚刚运行完成的节点
                completed_step = find_step(steps_list, current_node_id)
                if completed_step:
                    # 如果它之前是 'running'，现在更新它的最终状态
                    if completed_step['status'] == 'running' or completed_step['status'] == 'pending':
                        completed_step['end_time'] = time.time()
                        feedback = state_update_from_node.get('feedback', '')

                        if "FAIL:" in feedback:
                            completed_step['status'] = 'failed'
                            # 将详细错误信息记录到步骤日志
                            completed_step['log'] = feedback.split('FAIL:', 1)[-1].strip()
                            _log(workflow_id, f"步骤 '{completed_step['name']}' FAILED.")
                        else:
                            completed_step['status'] = 'completed'
                            _log(workflow_id, f"步骤 '{completed_step['name']}' 已完成.")

                        # 如果有产出物，也记录下来 (可选)
                        if 'output' in state_update_from_node:
                            completed_step['output'] = str(state_update_from_node['output'])

                # d) 更新工作流的总体状态
                if current_node_id == "pre_deployment_pause":
                    workflow_data['status'] = "PAUSED"
                elif current_node_id == END or current_node_id == "__end__":
                    # 只有在图自然结束后才标记为COMPLETED
                    if workflow_data['status'] != "PAUSED":
                        workflow_data['status'] = "COMPLETED"
                else:
                    workflow_data['status'] = "RUNNING"

                # 找到下一个将要运行的节点并标记为 'running' (这是一个最佳实践，但较难实现)
                # 为了简化，我们只在日志中打印节点的开始信息
                # _log(workflow_id, f"步骤 '{next_node_name}' 已开始.")

                # --- 6. 保存更新后的状态 ---
                workflow_data['latest_state'] = latest_state
                _save_state(workflow_id, workflow_data)

            # --- 7. 循环结束后的最终处理 ---
            # 循环结束后，再次加载状态，检查是否需要更新最终状态
            final_workflow_data = _load_state(workflow_id)
            if final_workflow_data and final_workflow_data['status'] == "RUNNING":
                final_workflow_data['status'] = 'COMPLETED'
                _save_state(workflow_id, final_workflow_data)
                _log(workflow_id, "工作流 COMPLETED.")

                # 【修复】工作流完成后，自动为所有设备添加日志主题监控
                _auto_setup_all_devices_logging(final_workflow_data['latest_state'])

        except Exception as e:
            # 异常处理逻辑保持不变
            error_message = f"工作流线程 {workflow_id} 崩溃: {e}"
            print(error_message)
            traceback.print_exc()

            workflow_data = _load_state(workflow_id)
            if workflow_data:
                workflow_data['status'] = "FAILED"
                steps_list = workflow_data['latest_state']['workflow_steps']
                # 查找最后一个状态为 'running' 的步骤并标记为失败
                running_step = next((s for s in steps_list if s['status'] == 'running'), None)
                if running_step:
                    running_step['status'] = 'failed'
                    error_log_content = f"ERROR: {str(e)}\n{traceback.format_exc()}"
                    running_step['log'] = error_log_content
                    running_step['end_time'] = time.time()
                    _log(workflow_id, f"步骤 '{running_step['name']}' FAILED.")
                _save_state(workflow_id, workflow_data)


def start_workflow(user_id: int, request_data: dict) -> dict:
    """启动一个新的工作流，并将初始状态存入数据库。"""
    # 生成简短的工作流ID：wf + 时间戳后4位 + 随机字符
    import time
    timestamp_suffix = str(int(time.time()))[-4:]  # 时间戳后4位
    random_suffix = str(uuid.uuid4())[:4]  # UUID前4位
    workflow_id = f"wf-{timestamp_suffix}-{random_suffix}"
    device_tasks_from_request = request_data.get('device_tasks', [])
    if not device_tasks_from_request:
        raise ValueError("Request must contain a list of device_tasks.")

    for task in device_tasks_from_request:
        device_id = task.get('internal_device_id')
        if not device_id:
            raise ValueError(f"Device task for role '{task.get('device_role')}' is missing an internal_device_id.")
        device = Device.query.filter_by(internal_device_id=device_id, user_id=user_id).first()
        if not device:
            raise ValueError(f"Device with ID {device_id} not found or does not belong to the user.")
        task['board'] = device.board_model

    project_root = Path(__file__).resolve().parent.parent
    workspace_path = project_root / "temp_workspaces" / workflow_id
    workspace_path.mkdir(parents=True, exist_ok=True)

    # 【性能优化修正】确保共享缓存目录在工作流开始时就存在
    cache_path = project_root / "temp_workspaces" / ".build_cache"
    cache_path.mkdir(exist_ok=True)
    print(f"--- [Workflow Start] Ensured shared build cache exists at: {cache_path} ---")

    initial_steps: list[WorkflowStep] = []
    for node_id in NODE_FRIENDLY_NAMES.keys():
        initial_steps.append({
            "id": node_id,
            "name": NODE_FRIENDLY_NAMES.get(node_id, node_id.replace("_", " ").title()),
            "status": "pending", "log": "", "start_time": 0.0, "end_time": 0.0, "output": None,
        })

    communication_plan = request_data.get('inter_device_communication', [])

    # 从数据库加载用户和设备信息以填充初始状态
    user = User.query.get(user_id)
    initial_wifi_ssid = user.wifi_ssid if user else ""
    initial_wifi_password = user.wifi_password if user else ""

    initial_state = AgentState(
        workflow_id=workflow_id, user_id=user_id,
        project_name=request_data.get('project_name', '未命名多设备项目'),
        status="RUNNING", workflow_steps=initial_steps,
        user_input=request_data.get('project_description', ''),
        device_tasks_queue=device_tasks_from_request,
        system_plan={"communication": communication_plan},
        workspace_path=str(workspace_path.resolve()),
        available_actions=[], current_device_task=None, current_api_spec=None,
        module_tasks=[], current_module_task=None, completed_modules={},
        feedback="", project_files={}, test_plan=None, original_module_plan=None,
        build_dir="", firmware_path=None, deployment_choice=None,
        dp_info_list=[], faulty_module=None, user_action=None,
        device_dp_contract=[],  # V2.0 CONTRACT-FIRST: 新增DP契约字段
        unified_communication_contract=None,  # 【修复】新增统一通信契约字段
        # 填充初始的WiFi信息
        wifi_ssid=initial_wifi_ssid,
        wifi_password=initial_wifi_password,
        cloud_product_id=None,
        cloud_device_id=None,
        cloud_device_secret=None
    )

    workflow_data = {"status": "STARTING", "latest_state": initial_state}
    _save_state(workflow_id, workflow_data)
    _log(workflow_id, f"New workflow created for project: {initial_state['project_name']}")

    app = current_app._get_current_object()
    thread = threading.Thread(target=_run_graph_in_thread, args=(app, workflow_id, initial_state))
    thread.daemon = True
    thread.start()

    return {"workflow_id": workflow_id, "status": "RUNNING", "workflow_steps": initial_steps, "available_actions": []}

def get_workflow_status(workflow_id: str) -> dict:
    """从数据库获取工作流状态。"""
    workflow_data = _load_state(workflow_id)
    if not workflow_data:
        raise ValueError("Workflow not found.")

    status = workflow_data['status']
    state = workflow_data['latest_state']

    available_actions = []
    if status == "PAUSED":
        available_actions = state.get('available_actions', [])
        if not available_actions:
            available_actions.extend(["DEPLOY_USB", "DEPLOY_OTA"])

    # 【修复】直接从数据库记录获取logs字段，而不是从workflow_data
    record = WorkflowState.query.get(workflow_id)
    logs = record.logs if record else ''
    print(f"[DEBUG] 返回日志长度: {len(logs)} 字符")  # 调试信息

    return {
        "workflow_id": workflow_id,
        "status": status,
        "workflow_steps": state.get('workflow_steps', []),
        "available_actions": available_actions,
        "current_device_task": state.get('current_device_task'),  # 新增：返回当前设备信息
        "logs": logs  # 【修复】添加实时日志返回
    }


def _auto_setup_device_logging(state: dict):
    """工作流完成后，自动为设备设置日志主题监控"""
    try:
        from app.services.mqtt_service import mqtt_monitor

        # 获取当前设备任务信息
        current_device_task = state.get('current_device_task')
        if not current_device_task:
            print("未找到当前设备任务，跳过MQTT监控配置")
            return

        device_id = current_device_task.get('internal_device_id')
        if not device_id:
            print("设备ID为空，跳过MQTT监控配置")
            return

        # 【修复】从统一通信契约中获取设备的实际数据发送主题
        unified_contract = state.get('unified_communication_contract')
        device_role = current_device_task.get('device_role')

        # 基础日志主题
        log_topics = [
            f"/log/{device_id}",           # 通用日志主题
            f"/debug/{device_id}",         # 调试日志主题
            f"/status/{device_id}",        # 状态日志主题
        ]

        # 【新增】添加设备实际发送数据的主题
        if unified_contract and device_role:
            topic_map = unified_contract.get('topic_map', {})
            device_topics = topic_map.get(device_role, {})

            # 添加设备发布的数据主题
            pub_topics = device_topics.get('pub', [])
            for topic in pub_topics:
                if topic and topic not in log_topics:
                    log_topics.append(topic)
                    print(f"添加设备 {device_id} 的数据发布主题: {topic}")

            # 添加设备订阅的主题（用于监控设备接收的数据）
            sub_topics = device_topics.get('sub', [])
            for topic in sub_topics:
                if topic and topic not in log_topics:
                    log_topics.append(topic)
                    print(f"添加设备 {device_id} 的数据订阅主题: {topic}")

        # 【新增】从测试计划中获取验证主题
        test_plan = state.get('test_plan')
        if test_plan and isinstance(test_plan, dict):
            device_log_topic = test_plan.get('device_log_topic')
            if device_log_topic and device_log_topic not in log_topics:
                log_topics.append(device_log_topic)
                print(f"添加设备 {device_id} 的验证主题: {device_log_topic}")

        # 动态添加主题监控
        success = mqtt_monitor.update_device_topics(device_id, log_topics)
        if success:
            print(f"✅ 已为设备 {device_id} 自动添加MQTT主题监控: {log_topics}")
            _log(state.get('workflow_id'), f"已为设备 {device_id} 配置MQTT监控，监听 {len(log_topics)} 个主题")
        else:
            print(f"❌ 为设备 {device_id} 添加MQTT主题监控失败")

    except Exception as e:
        print(f"自动设置设备日志监控失败: {str(e)}")
        import traceback
        traceback.print_exc()


def _auto_setup_all_devices_logging(state: dict):
    """工作流完成后，自动为所有设备设置MQTT主题监控"""
    try:
        # 获取所有设备任务
        device_tasks_queue = state.get('device_tasks_queue', [])
        original_device_tasks = state.get('original_device_tasks', device_tasks_queue)

        # 如果没有设备任务队列，尝试从当前设备任务获取
        if not original_device_tasks and state.get('current_device_task'):
            original_device_tasks = [state.get('current_device_task')]

        if not original_device_tasks:
            print("未找到设备任务列表，跳过MQTT监控配置")
            return

        print(f"开始为 {len(original_device_tasks)} 个设备配置MQTT监控...")

        # 为每个设备配置监控
        for device_task in original_device_tasks:
            if not isinstance(device_task, dict):
                continue

            # 创建临时状态用于单个设备配置
            temp_state = state.copy()
            temp_state['current_device_task'] = device_task

            # 为单个设备配置监控
            _auto_setup_device_logging(temp_state)

        workflow_id = state.get('workflow_id')
        _log(workflow_id, f"已完成所有设备的MQTT监控配置")

    except Exception as e:
        print(f"批量设置设备MQTT监控失败: {str(e)}")
        import traceback
        traceback.print_exc()


def post_workflow_action(workflow_id: str, action_data: dict):
    """接收用户操作，更新数据库状态，并启动恢复线程。"""
    action = action_data.get("action")
    workflow_data = _load_state(workflow_id)

    if not workflow_data:
        raise ValueError("Workflow not found.")
    if workflow_data['status'] != 'PAUSED':
        raise ValueError(f"Workflow is in '{workflow_data['status']}' state, cannot perform actions.")

    state_to_resume = workflow_data['latest_state']
    state_to_resume['user_action'] = action
    state_to_resume['deployment_choice'] = "usb" if action == "DEPLOY_USB" else "ota"

    workflow_data['status'] = "RUNNING"
    workflow_data['latest_state'] = state_to_resume
    _save_state(workflow_id, workflow_data)
    _log(workflow_id, f"Action '{action}' received. Resuming graph execution.")

    app = current_app._get_current_object()
    thread = threading.Thread(target=_run_graph_in_thread, args=(app, workflow_id, state_to_resume))
    thread.daemon = True
    thread.start()


# --- 文件操作函数 (保持不变) ---
def get_file_tree(workflow_id: str) -> list:
    workflow_data = _load_state(workflow_id)
    if not workflow_data:
        raise ValueError("Workflow not found.")

    workspace_path_str = workflow_data['latest_state'].get('workspace_path')
    if not workspace_path_str: return []

    workspace_path = Path(workspace_path_str)
    if not workspace_path.exists(): return []

    def build_tree(dir_path: Path):
        tree = []
        for item in sorted(dir_path.iterdir(), key=lambda x: (x.is_file(), x.name.lower())):
            if item.name.startswith('.'): continue
            node = {"name": item.name, "path": str(item.relative_to(workspace_path)).replace("\\", "/")}
            if item.is_dir():
                node["type"] = "folder"
                node["children"] = build_tree(item)
            else:
                node["type"] = "file"
            tree.append(node)
        return tree

    return build_tree(workspace_path)


def get_file_content(workflow_id: str, file_path: str) -> str:
    workflow_data = _load_state(workflow_id)
    if not workflow_data: raise ValueError("Workflow not found.")

    workspace_path = Path(workflow_data['latest_state']['workspace_path'])
    full_path = (workspace_path / file_path).resolve()

    if not str(full_path).startswith(str(workspace_path.resolve())):
        raise ValueError("Access denied: path is outside the workspace.")

    if not full_path.is_file():
        raise FileNotFoundError(f"File not found: {file_path}")

    return full_path.read_text(encoding='utf-8')


def save_file_content(workflow_id: str, file_path: str, content: str):
    workflow_data = _load_state(workflow_id)
    if not workflow_data: raise ValueError("Workflow not found.")

    workspace_path = Path(workflow_data['latest_state']['workspace_path'])
    full_path = (workspace_path / file_path).resolve()

    if not str(full_path).startswith(str(workspace_path.resolve())):
        raise ValueError("Access denied: path is outside the workspace.")

    if not full_path.exists():
        raise FileNotFoundError(f"File not found, cannot save: {file_path}")

    full_path.write_text(content, encoding='utf-8')
