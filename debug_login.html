<!DOCTYPE html>
<html>
<head>
    <title>Debug Login</title>
    <script>
        function testLogin() {
            console.log('Login function called');
            alert('Login works!');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            const btn = document.getElementById('login-button');
            if (btn) {
                btn.addEventListener('click', testLogin);
                console.log('Event listener added');
            } else {
                console.error('Button not found');
            }
        });
    </script>
</head>
<body>
    <button id="login-button">Test Login</button>
</body>
</html>
