<!DOCTYPE html>
<html>
<head>
    <title>Test Functions</title>
</head>
<body>
    <button onclick="toggleForms()">Test toggleForms</button>
    <button onclick="togglePasswordVisibility('test', this)">Test togglePasswordVisibility</button>
    
    <script>
    function toggleForms() {
        console.log('toggleForms called');
        alert('toggleForms works!');
    }
    
    function togglePasswordVisibility(inputId, button) {
        console.log('togglePasswordVisibility called with:', inputId, button);
        alert('togglePasswordVisibility works!');
    }
    
    console.log('Test functions loaded');
    </script>
</body>
</html>
